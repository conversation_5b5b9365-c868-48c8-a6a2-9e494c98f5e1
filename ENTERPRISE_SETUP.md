# QuickSight Enterprise Edition Setup Guide

## Current Status
You have QuickSight Enterprise Edition which provides several options for individual visual embedding without Capacity Pricing.

## Option 1: IAM Role-Based Registered User Embedding (Recommended)

### Step 1: Create IAM Role
Create an IAM role in your AWS console:

```json
{
    "Version": "2012-10-17",
    "Statement": [
        {
            "Effect": "Allow",
            "Principal": {
                "Service": "quicksight.amazonaws.com"
            },
            "Action": "sts:AssumeRole"
        }
    ]
}
```

### Step 2: Attach QuickSight Policy
Add this policy to your IAM role:

```json
{
    "Version": "2012-10-17",
    "Statement": [
        {
            "Effect": "Allow",
            "Action": [
                "quicksight:RegisterUser",
                "quicksight:GetDashboardEmbedUrl",
                "quicksight:GetAuthCode"
            ],
            "Resource": "*"
        }
    ]
}
```

### Step 3: Set Environment Variable
Add your role ARN to your environment:
```
AWS_ROLE_ARN=arn:aws:iam::288552100418:role/QuickSightEmbeddingRole
```

## Option 2: Dashboard Theme Modification

### In QuickSight Console:
1. Go to your dashboard: `b991ee42-96e1-4591-b999-f5457b751d07`
2. Click "Edit" → "Themes"
3. Create custom theme with yellow colors:
   - Primary: #FFD700 (Gold Yellow)
   - Secondary: #FFA500 (Orange)
   - Background: #FFFACD (Light Yellow)

## Option 3: Individual Visual Share URL (Current Working Solution)

The system now creates enhanced URLs targeting individual visuals:
- Uses Enterprise Edition parameters
- Disables unnecessary controls
- Attempts color customization via URL parameters

## What You Can Achieve Right Now:

### ✅ Available with Enterprise (No Extra Cost):
- Individual visual embedding (not full dashboard)
- Cleaner interface (no tabs, controls, etc.)
- Basic customization via URL parameters
- Responsive embedding

### ❌ Requires Capacity Pricing ($250/month):
- Remove "Powered by QuickSight" branding
- Full color theme control via API
- Anonymous user embedding
- Advanced customization options

## Next Steps:
1. Try the IAM role setup for better embedding
2. Modify dashboard theme in QuickSight console for yellow colors
3. Consider Capacity Pricing if branding removal is critical