# Additional Cleanup Suggestions

## Unused Radix UI Dependencies
These could be removed from package.json if not needed for future features:

```json
// Potentially unused (check if needed for future features):
"@radix-ui/react-accordion": "^1.2.4",
"@radix-ui/react-aspect-ratio": "^1.1.3", 
"@radix-ui/react-avatar": "^1.1.4",
"@radix-ui/react-checkbox": "^1.1.5",
"@radix-ui/react-collapsible": "^1.1.4",
"@radix-ui/react-context-menu": "^2.2.7",
"@radix-ui/react-dropdown-menu": "^2.1.7",
"@radix-ui/react-hover-card": "^1.1.7",
"@radix-ui/react-menubar": "^1.1.7",
"@radix-ui/react-navigation-menu": "^1.2.6",
"@radix-ui/react-popover": "^1.1.7",
"@radix-ui/react-progress": "^1.1.3",
"@radix-ui/react-radio-group": "^1.2.4",
"@radix-ui/react-scroll-area": "^1.2.4",
"@radix-ui/react-select": "^2.1.7",
"@radix-ui/react-separator": "^1.1.3",
"@radix-ui/react-slider": "^1.2.4",
"@radix-ui/react-switch": "^1.1.4",
"@radix-ui/react-tabs": "^1.1.4",
"@radix-ui/react-toggle": "^1.1.3",
"@radix-ui/react-toggle-group": "^1.1.3",
```

## Other Potentially Unused Dependencies
```json
"@hookform/resolvers": "^3.10.0", // If no forms
"react-hook-form": "^7.55.0", // If no forms
"input-otp": "^1.4.2", // If no OTP input
"react-day-picker": "^8.10.1", // If no date picker
"embla-carousel-react": "^8.6.0", // If no carousel
"vaul": "^1.1.2", // If no drawer component
"cmdk": "^1.1.1", // If no command palette
```

## Keep These (Currently Used)
```json
// Core UI components in use:
"@radix-ui/react-alert-dialog": "^1.1.7", // ErrorBoundary
"@radix-ui/react-dialog": "^1.1.7", // Toaster/Sheet dependencies  
"@radix-ui/react-label": "^2.1.3", // Button/Form dependencies
"@radix-ui/react-slot": "^1.2.0", // Button component
"@radix-ui/react-toast": "^1.2.7", // Toaster
"@radix-ui/react-tooltip": "^1.2.0", // TooltipProvider
```