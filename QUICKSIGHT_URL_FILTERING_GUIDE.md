# QuickSight URL Filtering Implementation Guide

## Overview

AWS QuickSight supports URL-based filtering through parameters. This guide explains how to properly implement URL filtering for embedded QuickSight dashboards.

## Key Concepts

### 1. QuickSight Parameters vs Filters

- **Parameters**: Named variables defined in the QuickSight dashboard that can accept values
- **Filters**: Actual filtering logic that uses parameter values to filter data
- **URL Parameters**: Values passed through the URL using the `#p.parameterName=value` format

### 2. URL Parameter Format

QuickSight uses a specific format for URL parameters:

```
https://your-domain.com/dashboard#p.state=Texas&p.year=2023
```

Key points:
- Parameters are passed as hash fragments (after `#`)
- Each parameter starts with `p.` prefix
- Multiple parameters are separated by `&`
- For multiple values of the same parameter, repeat the parameter:
  ```
  #p.state=Texas&p.state=California&p.state=Nevada
  ```

## Implementation Steps

### Step 1: Create Parameters in QuickSight Dashboard

1. Open your dashboard in QuickSight Analysis mode
2. Click "Parameters" in the left sidebar
3. Create parameters for each filter you want to support:
   - Name: `state` (or any name you prefer)
   - Data type: String
   - Default value: (optional)
   - Allow multiple values: Yes (if needed)

### Step 2: Create Filters Using Parameters

1. Click "Filter" in the top menu
2. Add a filter for the field you want to filter
3. Choose "Custom filter"
4. Select "Use parameters"
5. Choose the parameter you created
6. Set the scope (all visuals, specific visuals, etc.)

### Step 3: Implement URL Parameter Handling in Your Application

```typescript
// Parse QuickSight parameters from URL
function parseQuickSightParameters(): Record<string, string | string[]> {
  const hash = window.location.hash;
  const params: Record<string, string | string[]> = {};
  
  if (!hash || !hash.startsWith('#')) {
    return params;
  }
  
  const paramString = hash.substring(1);
  const paramPairs = paramString.split('&');
  
  paramPairs.forEach(pair => {
    const [key, value] = pair.split('=');
    if (key && key.startsWith('p.')) {
      const paramName = key.substring(2);
      const decodedValue = decodeURIComponent(value || '');
      
      // Handle multiple values
      if (params[paramName]) {
        if (Array.isArray(params[paramName])) {
          (params[paramName] as string[]).push(decodedValue);
        } else {
          params[paramName] = [params[paramName] as string, decodedValue];
        }
      } else {
        params[paramName] = decodedValue;
      }
    }
  });
  
  return params;
}

// Generate QuickSight-compatible URL
function generateQuickSightURL(baseUrl: string, filters: Record<string, any>): string {
  const params: string[] = [];
  
  Object.entries(filters).forEach(([key, value]) => {
    if (value !== undefined && value !== null && value !== '') {
      if (Array.isArray(value)) {
        value.forEach(v => {
          params.push(`p.${key}=${encodeURIComponent(v)}`);
        });
      } else {
        params.push(`p.${key}=${encodeURIComponent(value)}`);
      }
    }
  });
  
  return baseUrl + (params.length > 0 ? '#' + params.join('&') : '');
}
```

### Step 4: Pass Parameters to Embedded Dashboard

When embedding the dashboard, the parameters in the URL will automatically be applied if:
1. The parameters exist in the dashboard
2. The parameters are connected to filters
3. The embed URL includes the hash parameters

## Example Implementation

### 1. URL with Single Parameter
```
http://localhost:3000/dashboard#p.state=Texas
```

### 2. URL with Multiple Parameters
```
http://localhost:3000/dashboard#p.state=Texas&p.year=2023&p.category=Sales
```

### 3. URL with Multi-value Parameter
```
http://localhost:3000/dashboard#p.state=Texas&p.state=California&p.state=Nevada
```

## Common Issues and Solutions

### Issue 1: Filters Not Applied
**Solution**: Ensure parameters are created in the QuickSight dashboard and connected to filters

### Issue 2: Invalid Parameter Values
**Solution**: Parameter values must match exactly what's in your data (case-sensitive)

### Issue 3: Cross-Origin Issues
**Solution**: Ensure your domain is whitelisted in QuickSight settings

### Issue 4: Parameters Not Recognized
**Solution**: Use the correct format with `#p.` prefix, not regular query parameters

## Testing Your Implementation

1. Create a simple parameter in your dashboard (e.g., `state`)
2. Connect it to a filter
3. Test with a direct URL: `your-dashboard-url#p.state=Texas`
4. Verify the dashboard filters to show only Texas data

## Advanced Features

### Dynamic Default Values
You can set dynamic defaults per user in QuickSight parameters

### Cascading Parameters
Parameters can depend on other parameters for cascading filters

### Parameter Controls
Add dropdown controls in the dashboard for manual parameter changes

## Best Practices

1. **Parameter Naming**: Use clear, descriptive names
2. **Default Values**: Set sensible defaults for better UX
3. **Validation**: Validate parameter values before passing to URL
4. **Documentation**: Document available parameters for your users
5. **Error Handling**: Handle cases where parameters don't match data

## Resources

- [AWS QuickSight Parameters Documentation](https://docs.aws.amazon.com/quicksight/latest/user/parameters-in-quicksight.html)
- [Using Parameters in URLs](https://docs.aws.amazon.com/quicksight/latest/user/parameters-in-a-url.html)
- [QuickSight Embedding SDK](https://github.com/awslabs/amazon-quicksight-embedding-sdk) 