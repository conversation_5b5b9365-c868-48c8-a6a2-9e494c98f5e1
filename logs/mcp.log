{"level":"info","message":"StdioMcpServer initialized","service":"mcp-server","timestamp":"2025-07-18T01:55:39.718Z"}
{"level":"info","message":"Starting MCP server with stdio transport","service":"mcp-server","timestamp":"2025-07-18T01:55:39.718Z"}
{"level":"info","message":"MCP server started with stdio transport","service":"mcp-server","timestamp":"2025-07-18T01:55:39.720Z"}
{"level":"info","message":"StdioMcpServer initialized","service":"mcp-server","timestamp":"2025-07-18T01:57:42.647Z"}
{"level":"info","message":"Starting MCP server with stdio transport","service":"mcp-server","timestamp":"2025-07-18T01:57:42.648Z"}
{"level":"info","message":"MCP server started with stdio transport","service":"mcp-server","timestamp":"2025-07-18T01:57:42.648Z"}
{"level":"info","message":"StdioMcpServer initialized","service":"mcp-server","timestamp":"2025-07-18T01:59:01.610Z"}
{"level":"info","message":"Starting MCP server with stdio transport","service":"mcp-server","timestamp":"2025-07-18T01:59:01.610Z"}
{"level":"info","message":"MCP server started with stdio transport","service":"mcp-server","timestamp":"2025-07-18T01:59:01.611Z"}
