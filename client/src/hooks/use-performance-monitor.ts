import { useEffect, useRef, useState } from 'react';

interface PerformanceMetrics {
  loadTime: number | null;
  embedTime: number | null;
  renderTime: number | null;
  totalTime: number | null;
}

export function usePerformanceMonitor() {
  const [metrics, setMetrics] = useState<PerformanceMetrics>({
    loadTime: null,
    embedTime: null,
    renderTime: null,
    totalTime: null
  });

  const startTimeRef = useRef<number>(Date.now());
  const embedStartRef = useRef<number | null>(null);
  const renderStartRef = useRef<number | null>(null);

  const startEmbed = () => {
    embedStartRef.current = Date.now();
  };

  const endEmbed = () => {
    if (embedStartRef.current) {
      const embedTime = Date.now() - embedStartRef.current;
      setMetrics(prev => ({ ...prev, embedTime }));
      renderStartRef.current = Date.now();
    }
  };

  const endRender = () => {
    const now = Date.now();
    if (renderStartRef.current) {
      const renderTime = now - renderStartRef.current;
      const totalTime = now - startTimeRef.current;
      setMetrics(prev => ({ 
        ...prev, 
        renderTime, 
        totalTime,
        loadTime: totalTime - (prev.embedTime || 0) - renderTime
      }));
    }
  };

  const reset = () => {
    startTimeRef.current = Date.now();
    embedStartRef.current = null;
    renderStartRef.current = null;
    setMetrics({
      loadTime: null,
      embedTime: null,
      renderTime: null,
      totalTime: null
    });
  };

  return {
    metrics,
    startEmbed,
    endEmbed,
    endRender,
    reset
  };
}