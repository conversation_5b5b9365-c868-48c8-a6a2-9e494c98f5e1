@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --background: hsl(0, 0%, 100%);
  --foreground: hsl(20, 14.3%, 4.1%);
  --muted: hsl(60, 4.8%, 95.9%);
  --muted-foreground: hsl(25, 5.3%, 44.7%);
  --popover: hsl(0, 0%, 100%);
  --popover-foreground: hsl(20, 14.3%, 4.1%);
  --card: hsl(0, 0%, 100%);
  --card-foreground: hsl(20, 14.3%, 4.1%);
  --border: hsl(27, 6.8%, 84.7%);
  --input: hsl(27, 6.8%, 84.7%);
  --primary: hsl(36, 100%, 50%);
  --primary-foreground: hsl(0, 0%, 100%);
  --secondary: hsl(210, 35%, 19%);
  --secondary-foreground: hsl(0, 0%, 100%);
  --accent: hsl(208, 70%, 39%);
  --accent-foreground: hsl(0, 0%, 100%);
  --destructive: hsl(0, 84.2%, 60.2%);
  --destructive-foreground: hsl(60, 9.1%, 97.8%);
  --ring: hsl(20, 14.3%, 4.1%);
  --radius: 0.5rem;
  
  /* AWS-inspired colors */
  --aws-orange: hsl(36, 100%, 50%);
  --aws-dark-blue: hsl(210, 35%, 19%);
  --aws-light-grey: hsl(0, 0%, 98%);
  --aws-dark-grey: hsl(210, 13%, 10%);
  --aws-blue: hsl(208, 70%, 39%);
  --aws-border: hsl(210, 8%, 84%);
}

.dark {
  --background: hsl(240, 10%, 3.9%);
  --foreground: hsl(0, 0%, 98%);
  --muted: hsl(240, 3.7%, 15.9%);
  --muted-foreground: hsl(240, 5%, 64.9%);
  --popover: hsl(240, 10%, 3.9%);
  --popover-foreground: hsl(0, 0%, 98%);
  --card: hsl(240, 10%, 3.9%);
  --card-foreground: hsl(0, 0%, 98%);
  --border: hsl(240, 3.7%, 15.9%);
  --input: hsl(240, 3.7%, 15.9%);
  --primary: hsl(36, 100%, 50%);
  --primary-foreground: hsl(0, 0%, 100%);
  --secondary: hsl(240, 3.7%, 15.9%);
  --secondary-foreground: hsl(0, 0%, 98%);
  --accent: hsl(240, 3.7%, 15.9%);
  --accent-foreground: hsl(0, 0%, 98%);
  --destructive: hsl(0, 62.8%, 30.6%);
  --destructive-foreground: hsl(0, 0%, 98%);
  --ring: hsl(240, 4.9%, 83.9%);
  --radius: 0.5rem;
  
  /* AWS-inspired colors for dark mode */
  --aws-orange: hsl(36, 100%, 50%);
  --aws-dark-blue: hsl(210, 35%, 19%);
  --aws-light-grey: hsl(240, 3.7%, 15.9%);
  --aws-dark-grey: hsl(0, 0%, 98%);
  --aws-blue: hsl(208, 70%, 50%);
  --aws-border: hsl(240, 3.7%, 25%);
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply font-sans antialiased bg-background text-foreground;
  }
}

/* QuickSight customization */
#quicksight-container iframe {
  border: none !important;
  outline: none !important;
}

/* Hide QuickSight branding and controls */
iframe[src*="quicksight"] {
  border: none !important;
  background: transparent !important;
}

/* Custom styling for QuickSight visual container */
.quicksight-embed-container {
  border: none !important;
  box-shadow: none !important;
  background: transparent !important;
}

@layer utilities {
  .aws-orange {
    color: var(--aws-orange);
  }
  
  .bg-aws-orange {
    background-color: var(--aws-orange);
  }
  
  .aws-dark-blue {
    color: var(--aws-dark-blue);
  }
  
  .bg-aws-dark-blue {
    background-color: var(--aws-dark-blue);
  }
  
  .aws-light-grey {
    color: var(--aws-light-grey);
  }
  
  .bg-aws-light-grey {
    background-color: var(--aws-light-grey);
  }
  
  .aws-dark-grey {
    color: var(--aws-dark-grey);
  }
  
  .bg-aws-dark-grey {
    background-color: var(--aws-dark-grey);
  }
  
  .aws-blue {
    color: var(--aws-blue);
  }
  
  .bg-aws-blue {
    background-color: var(--aws-blue);
  }
  
  .border-aws-border {
    border-color: var(--aws-border);
  }
}
