import { useState, useEffect, useRef, useCallback } from 'react';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Button } from '@/components/ui/button';
import { Loader2, RefreshCw, AlertCircle } from 'lucide-react';
import { parseQuickSightParameters } from '@/lib/quicksight-runtime-filtering';
import { createEmbeddingContext } from 'amazon-quicksight-embedding-sdk';
import '../styles/quicksight-custom.css';

const US_STATES = [
  'Alabama', 'Alaska', 'Arizona', 'Arkansas', 'California', 'Colorado', 'Connecticut', 'Delaware',
  'Florida', 'Georgia', 'Hawaii', 'Idaho', 'Illinois', 'Indiana', 'Iowa', 'Kansas', 'Kentucky',
  'Louisiana', 'Maine', 'Maryland', 'Massachusetts', 'Michigan', 'Minnesota', 'Mississippi',
  'Missouri', 'Montana', 'Nebraska', 'Nevada', 'New Hampshire', 'New Jersey', 'New Mexico',
  'New York', 'North Carolina', 'North Dakota', 'Ohio', 'Oklahoma', 'Oregon', 'Pennsylvania',
  'Rhode Island', 'South Carolina', 'South Dakota', 'Tennessee', 'Texas', 'Utah', 'Vermont',
  'Virginia', 'Washington', 'West Virginia', 'Wisconsin', 'Wyoming'
];

export default function Dashboard() {
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [urlParameters, setUrlParameters] = useState<Record<string, string | string[]>>({});
  const [selectedState, setSelectedState] = useState<string>('');
  const [containerKey, setContainerKey] = useState(Date.now());
  const [retryCount, setRetryCount] = useState(0);
  const [isVisualMode, setIsVisualMode] = useState(true); // We're always in visual mode based on your request

  const containerRef = useRef<HTMLDivElement>(null);
  const embeddedVisualRef = useRef<any>(null);
  const isEmbeddingRef = useRef(false);

  // Initialize and handle URL parameter changes
  useEffect(() => {
    const updateParams = () => {
      const newParams = parseQuickSightParameters();
      setUrlParameters(newParams);
    };

    updateParams();
    window.addEventListener('hashchange', updateParams);
    return () => window.removeEventListener('hashchange', updateParams);
  }, []);

  // Enhanced visual embedding for better integration
  const embedVisual = useCallback(async (forceReload = false) => {
    if (isEmbeddingRef.current || !containerRef.current) {
      console.log('⏸️ Skipping embed - already embedding or container not ready');
      return;
    }

    isEmbeddingRef.current = true;

    try {
      setIsLoading(true);
      setError(null);
      setContainerKey(Date.now());

      // Clean up previous visual
      if (embeddedVisualRef.current) {
        console.log('🧹 Cleaning up previous visual');
        await embeddedVisualRef.current.close?.();
        embeddedVisualRef.current = null;
      }

      const quicksightParams = parseQuickSightParameters();
      console.log('🚀 Starting visual embed with parameters:', quicksightParams);
      console.log('�  Current URL:', window.location.href);
      console.log('🔄 Force reload:', forceReload);

      const response = await fetch('/api/quicksight/embed-url', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          dashboardId: "b991ee42-96e1-4591-b999-f5457b751d07",
          visualId: "b991ee42-96e1-4591-b999-f5457b751d07_6b78e4ed-c15d-4023-854d-386e496163d4",
          sheetId: "b991ee42-96e1-4591-b999-f5457b751d07_26c2afbe-9a56-4226-87bd-e776ece85ae7",
          accountId: "************",
          directoryAlias: "ARTBA2025",
          colorScheme: 'default',
          urlParameters: quicksightParams,
          embedType: 'visual'
        })
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`Failed to get embed URL: ${response.status} - ${errorText}`);
      }

      const responseData = await response.json();
      console.log('📡 API Response:', responseData);
      const { embedUrl } = responseData;
      
      // Log the exact embed URL for debugging
      console.log('🔍 Full embed URL:', embedUrl);
      console.log('🎯 Embed type:', responseData.embedType);
      console.log('📊 Visual ID from response:', responseData.visualId);

      // Small delay to ensure container is ready
      await new Promise(resolve => setTimeout(resolve, 100));

      if (!containerRef.current) throw new Error('Container not ready');

      console.log('🎯 Creating embedding context...');
      const embeddingContext = await createEmbeddingContext({
        onChange: (changeEvent, metadata) => {
          console.log('Context received a change', changeEvent, metadata);
        },
      });

      console.log('📊 Embedding visual with URL:', embedUrl);

      // Validate the embed URL format
      if (!embedUrl.includes('quicksight.aws.amazon.com')) {
        throw new Error('Invalid QuickSight embed URL format');
      }

      // Use embedDashboard for visual embedding (SDK compatibility with DashboardVisual experience)
      const embeddedVisual = await embeddingContext.embedDashboard(
        {
          url: embedUrl,
          container: containerRef.current,
          height: "100%",
          width: "100%",
          withIframePlaceholder: false,
          className: "quicksight-visual-embed",
          onChange: (changeEvent, metadata) => {
            switch (changeEvent.eventName) {
              case 'FRAME_MOUNTED':
                console.log('📦 Visual frame mounted');
                break;
              case 'FRAME_LOADED':
                console.log('🖼️ Visual frame loaded');
                break;
              default:
                console.log('📊 Frame event:', changeEvent.eventName);
            }
          }
        },
        {
          parameters: quicksightParams && Object.keys(quicksightParams).length > 0
            ? Object.entries(quicksightParams).map(([name, value]) => ({
                Name: name,
                Values: Array.isArray(value) ? value : [value]
              }))
            : [],
          locale: "en-US",
          onMessage: async (messageEvent, experienceMetadata) => {
            console.log('📨 QuickSight visual event:', messageEvent.eventName, messageEvent);

            switch (messageEvent.eventName) {
              case 'CONTENT_LOADED':
                console.log('✅ Visual content loaded successfully');
                console.log('📊 Visual title:', messageEvent.message?.title);
                setIsLoading(false);
                setRetryCount(0);

                // Apply custom styling to remove any remaining iframe appearance
                setTimeout(() => {
                  const iframe = containerRef.current?.querySelector('iframe');
                  if (iframe) {
                    console.log('🎨 Applying custom visual styling');
                    iframe.style.border = 'none';
                    iframe.style.borderRadius = '0';
                    iframe.style.boxShadow = 'none';
                    iframe.style.outline = 'none';
                    iframe.style.background = 'transparent';
                  }
                }, 500);
                break;

              case 'ERROR_OCCURRED':
                console.error('❌ Visual error:', messageEvent);
                console.error('Error code:', messageEvent.message?.errorCode);
                setError(`Visual failed to load: ${messageEvent.message?.errorCode || 'Unknown error'}`);
                setIsLoading(false);
                break;

              case 'PARAMETERS_CHANGED':
                console.log('🎛️ Parameters changed:', messageEvent.message?.changedParameters);
                break;

              case 'SIZE_CHANGED':
                console.log('📐 Size changed:', messageEvent.message);
                break;

              case 'VISUAL_RENDERED' as any:
                console.log('🎨 Visual rendered:', (messageEvent as any).message);
                break;

              case 'SHEET_SELECTED' as any:
                console.log('📄 Sheet selected:', (messageEvent as any).message);
                break;

              default:
                console.log('📨 Other QuickSight visual event:', messageEvent.eventName);
                break;
            }
          }
        }
      );

      embeddedVisualRef.current = embeddedVisual;
      console.log('✅ Visual embedding completed successfully');

      // Fallback timeout
      setTimeout(() => {
        setIsLoading(prev => {
          if (prev) {
            console.log('⏰ Fallback timeout: hiding loading spinner after 5 seconds');
            return false;
          }
          return prev;
        });
      }, 5000);

    } catch (error: any) {
      console.error('❌ Visual embed failed:', error);
      setError(error.message || 'Failed to load visual');
      setIsLoading(false);
      setRetryCount(prev => prev + 1);
    } finally {
      isEmbeddingRef.current = false;
    }
  }, []);

  // Initialize visual
  useEffect(() => {
    embedVisual();
    return () => {
      embeddedVisualRef.current?.close?.();
      if (containerRef.current) containerRef.current.innerHTML = '';
    };
  }, [embedVisual]);

  // Track previous parameters to avoid unnecessary re-embeds
  const prevParametersRef = useRef<Record<string, string | string[]>>({});

  // Re-embed visual when URL parameters change (debounced)
  useEffect(() => {
    // Check if parameters actually changed
    const prevParams = prevParametersRef.current;
    const paramsChanged = JSON.stringify(urlParameters) !== JSON.stringify(prevParams);

    if (!paramsChanged) {
      console.log('⏸️ Parameters unchanged, skipping re-embed');
      return;
    }

    // Skip if visual hasn't been initialized yet and we have no parameters
    if (!embeddedVisualRef.current && Object.keys(urlParameters).length === 0) {
      console.log('⏸️ Visual not initialized yet and no parameters, skipping re-embed');
      prevParametersRef.current = { ...urlParameters };
      return;
    }

    console.log('🔄 URL parameters changed, re-embedding visual');
    console.log('🔄 Previous params:', prevParams);
    console.log('🔄 New params:', urlParameters);

    const timeoutId = setTimeout(() => {
      if (!isEmbeddingRef.current) {
        console.log('🔄 Executing visual re-embed after debounce');
        embedVisual(true); // Force reload
        prevParametersRef.current = { ...urlParameters };
      } else {
        console.log('⏸️ Skipping re-embed - already embedding');
      }
    }, 300); // 300ms debounce

    return () => {
      console.log('🧹 Cleaning up re-embed timeout');
      clearTimeout(timeoutId);
    };
  }, [urlParameters, embedVisual]);

  const handleStateChange = useCallback((state: string) => {
    setSelectedState(state);
    const newUrl = state === 'all'
      ? `${window.location.origin}${window.location.pathname}`
      : `${window.location.origin}${window.location.pathname}#p.state=${state}`;

    window.history.pushState({}, '', newUrl);
    window.dispatchEvent(new HashChangeEvent('hashchange'));
  }, []);

  const handleRetry = useCallback(() => {
    console.log('🔄 Manual retry triggered');
    setError(null);
    embedVisual(true); // Force reload
  }, [embedVisual]);

  // Update selected state based on URL parameters
  useEffect(() => {
    const currentState = urlParameters.state;
    if (currentState && typeof currentState === 'string') {
      setSelectedState(currentState);
    } else if (!currentState) {
      setSelectedState('all');
    }
  }, [urlParameters]);

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Fixed Header with Controls */}
      <div className="fixed top-0 left-0 right-0 z-50 bg-white/95 backdrop-blur-sm border-b border-gray-200 shadow-sm">
        <div className="container mx-auto px-6 py-4">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold text-gray-900">
                Work Zone Safety Analytics
              </h1>
              <p className="text-sm text-gray-600">
                {isVisualMode ? 'Single visual view - Work Zone Fatalities Map' : 'Interactive visual analytics for work zone fatalities and safety insights'}
              </p>
            </div>

            {/* Compact State Filter */}
            <div className="flex items-center space-x-4">
              {isVisualMode && (
                <Badge variant="secondary" className="bg-blue-100 text-blue-800">
                  Visual Mode
                </Badge>
              )}
              <div className="flex items-center space-x-2">
                <label className="text-sm font-medium text-gray-700">Filter:</label>
                <Select value={selectedState} onValueChange={handleStateChange}>
                  <SelectTrigger className="w-48 h-9">
                    <SelectValue placeholder="Select state..." />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All States</SelectItem>
                    {US_STATES.map((state) => (
                      <SelectItem key={state} value={state}>
                        {state}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {selectedState && selectedState !== 'all' && (
                <Badge variant="secondary" className="text-xs">
                  {selectedState}
                </Badge>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Full-Screen Visual Container */}
      <div className="pt-24 h-screen">
        <div className="relative w-full h-full bg-white rounded-lg shadow-sm mx-4 mb-4" style={{ height: 'calc(100vh - 7rem)' }}>
          <div
            ref={containerRef}
            key={`visual-container-${containerKey}`}
            className="w-full h-full rounded-lg overflow-hidden"
            style={{
              background: 'white',
              border: 'none',
              margin: 0,
              padding: 0
            }}
          />

          {/* Loading State */}
          {isLoading && (
            <div className="absolute inset-0 flex items-center justify-center bg-white/95 backdrop-blur-sm rounded-lg">
              <div className="text-center p-8">
                <div className="relative">
                  <Loader2 className="h-12 w-12 animate-spin mx-auto mb-6 text-blue-600" />
                  <div className="absolute inset-0 h-12 w-12 mx-auto rounded-full border-2 border-blue-200 animate-pulse"></div>
                </div>
                <h3 className="text-xl font-semibold mb-2">Loading Visual</h3>
                <p className="text-gray-600">Preparing your analytics visualization...</p>
                {retryCount > 0 && (
                  <p className="text-sm text-gray-500 mt-3 px-4 py-2 bg-blue-50 rounded-full inline-block">
                    Attempt {retryCount + 1}
                  </p>
                )}
              </div>
            </div>
          )}

          {/* Error State */}
          {error && !isLoading && (
            <div className="absolute inset-0 flex items-center justify-center bg-white/95 backdrop-blur-sm rounded-lg">
              <div className="text-center max-w-md p-8">
                <div className="relative mb-6">
                  <AlertCircle className="h-16 w-16 text-red-500 mx-auto" />
                  <div className="absolute inset-0 h-16 w-16 mx-auto rounded-full border-2 border-red-200 animate-pulse"></div>
                </div>
                <h3 className="text-xl font-semibold mb-3">Visual Error</h3>
                <p className="text-gray-600 mb-6 leading-relaxed">{error}</p>
                <Button
                  onClick={handleRetry}
                  className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white px-6 py-3 rounded-lg shadow-lg transition-all duration-200 hover:shadow-xl"
                >
                  <RefreshCw className="h-4 w-4 mr-2" />
                  Retry Visual
                </Button>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}