// QuickSight Runtime Filtering Configuration
// Based on AWS Documentation: https://docs.aws.amazon.com/quicksight/latest/user/embedding-runtime-filtering.html
// And Context 7 MCP documentation for amazon-quicksight-embedding-sdk

import { QUICKSIGHT_CONFIG } from './quicksight-config';

export interface CategoryFilter {
  Column: {
    DataSetIdentifier: string;
    ColumnName: string;
  };
  FilterId: string;
  Configuration: {
    FilterListConfiguration: {
      MatchOperator: 'EQUALS' | 'DOES_NOT_EQUAL' | 'CONTAINS' | 'DOES_NOT_CONTAIN' | 'STARTS_WITH' | 'ENDS_WITH';
      CategoryValues: string[];
      NullOption?: 'ALL_VALUES' | 'NULLS_ONLY' | 'NON_NULLS_ONLY';
    };
  };
}

export interface NumericRangeFilter {
  Column: {
    DataSetIdentifier: string;
    ColumnName: string;
  };
  FilterId: string;
  NullOption: 'ALL_VALUES' | 'NULLS_ONLY' | 'NON_NULLS_ONLY';
  IncludeMaximum: boolean;
  IncludeMinimum: boolean;
  RangeMaximum?: {
    StaticValue: number;
  };
  RangeMinimum?: {
    StaticValue: number;
  };
}

export interface RelativeDatesFilter {
  Column: {
    DataSetIdentifier: string;
    ColumnName: string;
  };
  FilterId: string;
  AnchorDateConfiguration: {
    AnchorOption: 'NOW';
  };
  TimeGranularity: 'YEAR' | 'QUARTER' | 'MONTH' | 'WEEK' | 'DAY' | 'HOUR' | 'MINUTE' | 'SECOND';
  RelativeDateType: 'PREVIOUS' | 'THIS' | 'LAST' | 'NOW' | 'NEXT';
  NullOption: 'ALL_VALUES' | 'NULLS_ONLY' | 'NON_NULLS_ONLY';
  MinimumGranularity: 'YEAR' | 'QUARTER' | 'MONTH' | 'WEEK' | 'DAY' | 'HOUR' | 'MINUTE' | 'SECOND';
  RelativeDateValue: number;
}

export interface FilterGroup {
  FilterGroupId: string;
  Filters: Array<{
    CategoryFilter?: CategoryFilter;
    NumericRangeFilter?: NumericRangeFilter;
    RelativeDatesFilter?: RelativeDatesFilter;
  }>;
  ScopeConfiguration: {
    SelectedSheets: {
      SheetVisualScopingConfigurations: Array<{
        Scope: 'ALL_VISUALS' | 'SELECTED_VISUALS';
        VisualIds?: string[];
        SheetId: string;
      }>;
    };
  };
  CrossDataset: 'SINGLE_DATASET' | 'ALL_DATASETS';
  Status: 'ENABLED' | 'DISABLED';
}

// Legacy interface for backward compatibility
export interface QuickSightFilter {
  FilterId: string;
  CategoryFilter?: CategoryFilter;
  NumericRangeFilter?: NumericRangeFilter;
  RelativeDatesFilter?: RelativeDatesFilter;
}

// Enhanced filter creation utilities
export class FilterBuilder {
  private dataSetIdentifier: string;
  private sheetId: string;

  constructor(dataSetIdentifier: string, sheetId: string) {
    this.dataSetIdentifier = dataSetIdentifier;
    this.sheetId = sheetId;
  }

  // Create category filter
  createCategoryFilter(
    filterId: string,
    columnName: string,
    values: string[],
    matchOperator: 'EQUALS' | 'DOES_NOT_EQUAL' | 'CONTAINS' | 'DOES_NOT_CONTAIN' | 'STARTS_WITH' | 'ENDS_WITH' = 'EQUALS'
  ): FilterGroup {
    return {
      FilterGroupId: filterId,
      Filters: [{
        CategoryFilter: {
          Column: {
            DataSetIdentifier: this.dataSetIdentifier,
            ColumnName: columnName
          },
          FilterId: filterId,
          Configuration: {
            FilterListConfiguration: {
              MatchOperator: matchOperator,
              CategoryValues: values,
              NullOption: 'NON_NULLS_ONLY'
            }
          }
        }
      }],
      ScopeConfiguration: {
        SelectedSheets: {
          SheetVisualScopingConfigurations: [{
            Scope: 'ALL_VISUALS',
            SheetId: this.sheetId
          }]
        }
      },
      CrossDataset: 'SINGLE_DATASET',
      Status: 'ENABLED'
    };
  }

  // Create numeric range filter
  createNumericRangeFilter(
    filterId: string,
    columnName: string,
    minValue?: number,
    maxValue?: number,
    includeMinimum: boolean = true,
    includeMaximum: boolean = true
  ): FilterGroup {
    const filter: NumericRangeFilter = {
      Column: {
        DataSetIdentifier: this.dataSetIdentifier,
        ColumnName: columnName
      },
      FilterId: filterId,
      NullOption: 'NON_NULLS_ONLY',
      IncludeMaximum: includeMaximum,
      IncludeMinimum: includeMinimum
    };

    if (minValue !== undefined) {
      filter.RangeMinimum = { StaticValue: minValue };
    }
    if (maxValue !== undefined) {
      filter.RangeMaximum = { StaticValue: maxValue };
    }

    return {
      FilterGroupId: filterId,
      Filters: [{ NumericRangeFilter: filter }],
      ScopeConfiguration: {
        SelectedSheets: {
          SheetVisualScopingConfigurations: [{
            Scope: 'ALL_VISUALS',
            SheetId: this.sheetId
          }]
        }
      },
      CrossDataset: 'SINGLE_DATASET',
      Status: 'ENABLED'
    };
  }

  // Create relative date filter
  createRelativeDateFilter(
    filterId: string,
    columnName: string,
    relativeDateType: 'PREVIOUS' | 'THIS' | 'LAST' | 'NOW' | 'NEXT',
    relativeDateValue: number,
    timeGranularity: 'YEAR' | 'QUARTER' | 'MONTH' | 'WEEK' | 'DAY' = 'DAY'
  ): FilterGroup {
    return {
      FilterGroupId: filterId,
      Filters: [{
        RelativeDatesFilter: {
          Column: {
            DataSetIdentifier: this.dataSetIdentifier,
            ColumnName: columnName
          },
          FilterId: filterId,
          AnchorDateConfiguration: {
            AnchorOption: 'NOW'
          },
          TimeGranularity: timeGranularity,
          RelativeDateType: relativeDateType,
          NullOption: 'NON_NULLS_ONLY',
          MinimumGranularity: 'DAY',
          RelativeDateValue: relativeDateValue
        }
      }],
      ScopeConfiguration: {
        SelectedSheets: {
          SheetVisualScopingConfigurations: [{
            Scope: 'ALL_VISUALS',
            SheetId: this.sheetId
          }]
        }
      },
      CrossDataset: 'SINGLE_DATASET',
      Status: 'ENABLED'
    };
  }
}

// Enhanced URL parameter parsing
export function parseFiltersFromURL(): FilterGroup[] {
  const urlParams = new URLSearchParams(window.location.search);
  const filters: FilterGroup[] = [];
  
  console.log('🔍 Starting URL filter parsing...');
  console.log('🌐 Current URL:', window.location.href);
  console.log('📋 URL Parameters:', Object.fromEntries(urlParams));
  
  // Default configuration – pull identifiers from the central config so we only
  // need to update them in one place.
  const dataSetIdentifier = QUICKSIGHT_CONFIG.datasetIdentifier || QUICKSIGHT_CONFIG.dashboardId;

  // TODO: expose sheetId through config as well. For now we keep the existing
  // hard-coded value.
  const sheetId = 'b991ee42-96e1-4591-b999-f5457b751d07_26c2afbe-9a56-4226-87bd-e776ece85ae7'; // Real sheet ID from inspection
  const filterBuilder = new FilterBuilder(dataSetIdentifier, sheetId);

  // Parse state filter - use simplified approach with correct dataset reference
  const state = urlParams.get('state');
  if (state && state !== 'all') {
    console.log('🏛️ Creating state filter for:', state);
    
    // Create a single filter with the correct column name from the dashboard
    // Use a generic dataset reference that QuickSight can resolve
    const filter = {
      FilterGroupId: 'state-filter',
      Filters: [{
        CategoryFilter: {
          Column: {
            DataSetIdentifier: dataSetIdentifier, // Use the dashboard ID as dataset identifier
            ColumnName: 'State_Geo' // Correct column name from the dashboard
          },
          FilterId: 'state-category-filter',
          Configuration: {
            FilterListConfiguration: {
              MatchOperator: 'EQUALS' as const, // Changed to EQUALS for exact match
              CategoryValues: [state],
              NullOption: 'NON_NULLS_ONLY' as const
            }
          }
        }
      }],
      ScopeConfiguration: {
        SelectedSheets: {
          SheetVisualScopingConfigurations: [{
            Scope: 'ALL_VISUALS' as const,
            SheetId: sheetId
          }]
        }
      },
      CrossDataset: 'SINGLE_DATASET' as const,
      Status: 'ENABLED' as const
    };
    
    filters.push(filter);
    console.log(`   📊 Created state filter: State_Geo EQUALS ${state}`);
    console.log(`   🔧 Using dataset ID: ${dataSetIdentifier}`);
    console.log(`   📋 Using sheet ID: ${sheetId}`);
  }

  // Parse multiple states
  const states = urlParams.get('states');
  if (states && states !== 'all') {
    const stateList = states.split(',').map(s => s.trim());
    filters.push(filterBuilder.createCategoryFilter(
      'states-filter',
      'State_Geo',
      stateList,
      'EQUALS'
    ));
  }

  // Parse region filter
  const region = urlParams.get('region');
  if (region && region !== 'all') {
    filters.push(filterBuilder.createCategoryFilter(
      'region-filter',
      'Region',
      [region],
      'EQUALS'
    ));
  }

  // Parse multiple regions
  const regions = urlParams.get('regions');
  if (regions && regions !== 'all') {
    const regionList = regions.split(',').map(r => r.trim());
    filters.push(filterBuilder.createCategoryFilter(
      'regions-filter',
      'Region',
      regionList,
      'EQUALS'
    ));
  }

  // Parse year filter (common QuickSight column)
  const year = urlParams.get('year');
  if (year && year !== 'all') {
    filters.push(filterBuilder.createCategoryFilter(
      'year-filter',
      'Year',
      [year],
      'EQUALS'
    ));
  }

  // Parse multiple years
  const years = urlParams.get('years');
  if (years && years !== 'all') {
    const yearList = years.split(',').map(y => y.trim());
    filters.push(filterBuilder.createCategoryFilter(
      'years-filter',
      'Year',
      yearList,
      'EQUALS'
    ));
  }

  // Parse category filter (common QuickSight column)
  const category = urlParams.get('category');
  if (category && category !== 'all') {
    filters.push(filterBuilder.createCategoryFilter(
      'category-filter',
      'Category',
      [category],
      'EQUALS'
    ));
  }

  // Parse multiple categories
  const categories = urlParams.get('categories');
  if (categories && categories !== 'all') {
    const categoryList = categories.split(',').map(c => c.trim());
    filters.push(filterBuilder.createCategoryFilter(
      'categories-filter',
      'Category',
      categoryList,
      'EQUALS'
    ));
  }

  // Parse date range filter with enhanced options
  const dateRange = urlParams.get('dateRange');
  if (dateRange && dateRange !== 'all') {
    let relativeDateValue = 30;
    let timeGranularity: 'YEAR' | 'QUARTER' | 'MONTH' | 'WEEK' | 'DAY' = 'DAY';
    
    switch (dateRange) {
      case '7d':
        relativeDateValue = 7;
        timeGranularity = 'DAY';
        break;
      case '30d':
        relativeDateValue = 30;
        timeGranularity = 'DAY';
        break;
      case '90d':
        relativeDateValue = 90;
        timeGranularity = 'DAY';
        break;
      case '1y':
        relativeDateValue = 1;
        timeGranularity = 'YEAR';
        break;
      case '1m':
        relativeDateValue = 1;
        timeGranularity = 'MONTH';
        break;
      case '3m':
        relativeDateValue = 3;
        timeGranularity = 'MONTH';
        break;
      case '6m':
        relativeDateValue = 6;
        timeGranularity = 'MONTH';
        break;
      default:
        relativeDateValue = 30;
        timeGranularity = 'DAY';
    }

    filters.push(filterBuilder.createRelativeDateFilter(
      'date-filter',
      'Date', // Replace with your actual date column name
      'LAST',
      relativeDateValue,
      timeGranularity
    ));
  }

  // Parse service type filter
  const serviceType = urlParams.get('serviceType');
  if (serviceType && serviceType !== 'all') {
    filters.push(filterBuilder.createCategoryFilter(
      'service-filter',
      'ServiceType',
      [serviceType],
      'EQUALS'
    ));
  }

  // Parse multiple service types
  const serviceTypes = urlParams.get('serviceTypes');
  if (serviceTypes && serviceTypes !== 'all') {
    const serviceTypeList = serviceTypes.split(',').map(s => s.trim());
    filters.push(filterBuilder.createCategoryFilter(
      'service-types-filter',
      'ServiceType',
      serviceTypeList,
      'EQUALS'
    ));
  }

  // Parse numeric range filters
  const minValue = urlParams.get('minValue');
  const maxValue = urlParams.get('maxValue');
  if (minValue || maxValue) {
    filters.push(filterBuilder.createNumericRangeFilter(
      'value-range-filter',
      'Value', // Replace with your actual numeric column name
      minValue ? parseFloat(minValue) : undefined,
      maxValue ? parseFloat(maxValue) : undefined
    ));
  }

  // Parse custom text search
  const search = urlParams.get('search');
  if (search) {
    filters.push(filterBuilder.createCategoryFilter(
      'search-filter',
      'Description', // Replace with your searchable text column
      [search],
      'CONTAINS'
    ));
  }

  // Parse generic column filters (flexible approach)
  // This allows for any column name as a parameter
  const allParams = Array.from(urlParams.entries());
  const handledParams = new Set(['state', 'states', 'region', 'regions', 'year', 'years', 'category', 'categories', 'dateRange', 'serviceType', 'serviceTypes', 'minValue', 'maxValue', 'search']);
  
  allParams.forEach(([key, value]) => {
    if (!handledParams.has(key) && value && value !== 'all') {
      // Convert parameter name to proper column name (capitalize first letter)
      const columnName = key.charAt(0).toUpperCase() + key.slice(1);
      
      // Handle comma-separated values
      if (value.includes(',')) {
        const valueList = value.split(',').map(v => v.trim());
        filters.push(filterBuilder.createCategoryFilter(
          `${key}-filter`,
          columnName,
          valueList,
          'EQUALS'
        ));
      } else {
        filters.push(filterBuilder.createCategoryFilter(
          `${key}-filter`,
          columnName,
          [value],
          'EQUALS'
        ));
      }
    }
  });

  console.log('✅ URL filter parsing complete. Created', filters.length, 'filter(s)');
  filters.forEach((filterGroup, index) => {
    console.log(`   Filter Group ${index + 1}:`, filterGroup);
  });
  
  return filters;
}

// Enhanced URL generation with more filter types
export function generateFilteredURL(filters: { 
  state?: string;
  states?: string[];
  region?: string; 
  regions?: string[];
  year?: string;
  years?: string[];
  category?: string;
  categories?: string[];
  dateRange?: string; 
  serviceType?: string; 
  serviceTypes?: string[];
  minValue?: number;
  maxValue?: number;
  search?: string;
  [key: string]: any; // Allow any additional parameters
}): string {
  const params = new URLSearchParams();
  
  if (filters.state && filters.state !== 'all') {
    params.set('state', filters.state);
  }
  
  if (filters.states && filters.states.length > 0) {
    params.set('states', filters.states.join(','));
  }
  
  if (filters.region && filters.region !== 'all') {
    params.set('region', filters.region);
  }
  
  if (filters.regions && filters.regions.length > 0) {
    params.set('regions', filters.regions.join(','));
  }
  
  if (filters.year && filters.year !== 'all') {
    params.set('year', filters.year);
  }
  
  if (filters.years && filters.years.length > 0) {
    params.set('years', filters.years.join(','));
  }
  
  if (filters.category && filters.category !== 'all') {
    params.set('category', filters.category);
  }
  
  if (filters.categories && filters.categories.length > 0) {
    params.set('categories', filters.categories.join(','));
  }
  
  if (filters.dateRange && filters.dateRange !== 'all') {
    params.set('dateRange', filters.dateRange);
  }
  
  if (filters.serviceType && filters.serviceType !== 'all') {
    params.set('serviceType', filters.serviceType);
  }
  
  if (filters.serviceTypes && filters.serviceTypes.length > 0) {
    params.set('serviceTypes', filters.serviceTypes.join(','));
  }
  
  if (filters.minValue !== undefined) {
    params.set('minValue', filters.minValue.toString());
  }
  
  if (filters.maxValue !== undefined) {
    params.set('maxValue', filters.maxValue.toString());
  }
  
  if (filters.search) {
    params.set('search', filters.search);
  }
  
  // Handle any additional parameters
  const knownParams = new Set(['state', 'states', 'region', 'regions', 'year', 'years', 'category', 'categories', 'dateRange', 'serviceType', 'serviceTypes', 'minValue', 'maxValue', 'search']);
  Object.keys(filters).forEach(key => {
    if (!knownParams.has(key) && filters[key] !== undefined && filters[key] !== 'all') {
      if (Array.isArray(filters[key])) {
        params.set(key, filters[key].join(','));
      } else {
        params.set(key, filters[key].toString());
      }
    }
  });
  
  const paramString = params.toString();
  return paramString ? `${window.location.pathname}?${paramString}` : window.location.pathname;
}

// Enhanced URL update function
export function updateURLWithFilters(filters: { 
  state?: string;
  states?: string[];
  region?: string; 
  regions?: string[];
  year?: string;
  years?: string[];
  category?: string;
  categories?: string[];
  dateRange?: string; 
  serviceType?: string; 
  serviceTypes?: string[];
  minValue?: number;
  maxValue?: number;
  search?: string;
  [key: string]: any; // Allow any additional parameters
}): void {
  const newURL = generateFilteredURL(filters);
  window.history.pushState({}, '', newURL);
  console.log('🔗 Updated URL with filters:', newURL);
}

// Helper function to convert FilterGroups to legacy format for backward compatibility
export function convertFilterGroupsToLegacyFormat(filterGroups: FilterGroup[]): QuickSightFilter[] {
  const legacyFilters: QuickSightFilter[] = [];
  
  filterGroups.forEach(group => {
    group.Filters.forEach(filter => {
      if (filter.CategoryFilter) {
        legacyFilters.push({
          FilterId: filter.CategoryFilter.FilterId,
          CategoryFilter: filter.CategoryFilter
        });
      }
      if (filter.NumericRangeFilter) {
        legacyFilters.push({
          FilterId: filter.NumericRangeFilter.FilterId,
          NumericRangeFilter: filter.NumericRangeFilter
        });
      }
      if (filter.RelativeDatesFilter) {
        legacyFilters.push({
          FilterId: filter.RelativeDatesFilter.FilterId,
          RelativeDatesFilter: filter.RelativeDatesFilter
        });
      }
    });
  });
  
  return legacyFilters;
}

// Predefined filter templates for common use cases
export const filterTemplates = {
  // Regional filters
  createRegionFilter: (regions: string[], dataSetId: string, sheetId: string) => 
    new FilterBuilder(dataSetId, sheetId).createCategoryFilter('region-filter', 'Region', regions),
  
  // Time period filters
  createLastNDaysFilter: (days: number, dataSetId: string, sheetId: string) => 
    new FilterBuilder(dataSetId, sheetId).createRelativeDateFilter('date-filter', 'Date', 'LAST', days, 'DAY'),
  
  // Value range filters
  createValueRangeFilter: (min: number, max: number, columnName: string, dataSetId: string, sheetId: string) => 
    new FilterBuilder(dataSetId, sheetId).createNumericRangeFilter('value-range-filter', columnName, min, max),
  
  // Search filters
  createSearchFilter: (searchTerm: string, columnName: string, dataSetId: string, sheetId: string) => 
    new FilterBuilder(dataSetId, sheetId).createCategoryFilter('search-filter', columnName, [searchTerm], 'CONTAINS')
};

// Export the original functions for backward compatibility
export { parseFiltersFromURL as parseFiltersFromURL_Legacy };
export { generateFilteredURL as generateFilteredURL_Legacy };
export { updateURLWithFilters as updateURLWithFilters_Legacy };

// Convert regular URL parameters to QuickSight parameter format
export function convertToQuickSightParameters(filters: Record<string, any>): string {
  const params: string[] = [];
  
  Object.entries(filters).forEach(([key, value]) => {
    if (value !== undefined && value !== null && value !== '') {
      // Handle arrays (multiple values)
      if (Array.isArray(value)) {
        // For multiple values, repeat the parameter
        value.forEach(v => {
          params.push(`p.${key}=${encodeURIComponent(v)}`);
        });
      } else {
        // Single value
        params.push(`p.${key}=${encodeURIComponent(value)}`);
      }
    }
  });
  
  return params.length > 0 ? '#' + params.join('&') : '';
}

// Parse QuickSight parameters from URL hash
export function parseQuickSightParameters(): Record<string, string | string[]> {
  const hash = window.location.hash;
  const params: Record<string, string | string[]> = {};
  
  if (!hash || !hash.startsWith('#')) {
    return params;
  }
  
  // Remove the # and split by &
  const paramString = hash.substring(1);
  const paramPairs = paramString.split('&');
  
  paramPairs.forEach(pair => {
    const [key, value] = pair.split('=');
    
    if (key && key.startsWith('p.')) {
      const paramName = key.substring(2); // Remove 'p.' prefix
      const decodedValue = decodeURIComponent(value || '');
      
      // Handle multiple values for the same parameter
      if (params[paramName]) {
        if (Array.isArray(params[paramName])) {
          (params[paramName] as string[]).push(decodedValue);
        } else {
          params[paramName] = [params[paramName] as string, decodedValue];
        }
      } else {
        params[paramName] = decodedValue;
      }
    }
  });
  
  return params;
}

// Update the generateFilteredURL function to use QuickSight parameter format
export function generateQuickSightFilteredURL(baseUrl: string, filters: Record<string, any>): string {
  const url = new URL(baseUrl);
  const paramString = convertToQuickSightParameters(filters);
  return url.origin + url.pathname + url.search + paramString;
}