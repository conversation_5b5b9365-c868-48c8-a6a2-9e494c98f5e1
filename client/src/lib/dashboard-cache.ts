interface CacheEntry {
  url: string;
  timestamp: number;
  parameters: Record<string, any>;
}

class DashboardCache {
  private cache = new Map<string, CacheEntry>();
  private readonly TTL = 5 * 60 * 1000; // 5 minutes

  private generateKey(dashboardId: string, parameters: Record<string, any>): string {
    const sortedParams = Object.keys(parameters)
      .sort()
      .reduce((result, key) => {
        result[key] = parameters[key];
        return result;
      }, {} as Record<string, any>);
    
    return `${dashboardId}-${JSON.stringify(sortedParams)}`;
  }

  get(dashboardId: string, parameters: Record<string, any>): string | null {
    const key = this.generateKey(dashboardId, parameters);
    const entry = this.cache.get(key);
    
    if (!entry) return null;
    
    // Check if entry is expired
    if (Date.now() - entry.timestamp > this.TTL) {
      this.cache.delete(key);
      return null;
    }
    
    return entry.url;
  }

  set(dashboardId: string, parameters: Record<string, any>, url: string): void {
    const key = this.generateKey(dashboardId, parameters);
    this.cache.set(key, {
      url,
      timestamp: Date.now(),
      parameters: { ...parameters }
    });
  }

  clear(): void {
    this.cache.clear();
  }

  cleanup(): void {
    const now = Date.now();
    for (const [key, entry] of this.cache.entries()) {
      if (now - entry.timestamp > this.TTL) {
        this.cache.delete(key);
      }
    }
  }
}

export const dashboardCache = new DashboardCache();

// Cleanup expired entries every 5 minutes
setInterval(() => {
  dashboardCache.cleanup();
}, 5 * 60 * 1000);