// QuickSight Runtime Theming Configuration
// Based on AWS Documentation: https://docs.aws.amazon.com/quicksight/latest/user/embedding-runtime-theming.html

export interface ThemeConfiguration {
  Typography?: {
    FontFamilies?: Array<{
      FontFamily: string;
    }>;
  };
  Sheet?: {
    Tile?: {
      Border?: {
        show?: boolean;
      };
    };
  };
  UIColorPalette?: {
    PrimaryForeground?: string;
    PrimaryBackground?: string;
    SecondaryForeground?: string;
    SecondaryBackground?: string;
    Accent?: string;
    AccentForeground?: string;
    Danger?: string;
    DangerForeground?: string;
    Warning?: string;
    WarningForeground?: string;
    Success?: string;
    SuccessForeground?: string;
    Dimension?: string;
    DimensionForeground?: string;
    Measure?: string;
    MeasureForeground?: string;
  };
  DataColorPalette?: {
    Colors?: string[];
    MinMaxGradient?: string[];
    EmptyFillColor?: string;
  };
}

export interface ThemeOptions {
  themeArn?: string;
  themeOverride?: ThemeConfiguration;
  preloadThemes?: string[];
}

// Predefined theme configurations for different color schemes
export const themeConfigurations = {
  yellow: {
    UIColorPalette: {
      PrimaryForeground: '#000000',
      PrimaryBackground: '#FFFACD',
      SecondaryForeground: '#333333',
      SecondaryBackground: '#FFF8DC',
      Accent: '#FFD700',
      AccentForeground: '#000000',
      Danger: '#DC3545',
      DangerForeground: '#FFFFFF',
      Warning: '#FFC107',
      WarningForeground: '#000000',
      Success: '#28A745',
      SuccessForeground: '#FFFFFF',
      Dimension: '#B8860B',
      DimensionForeground: '#FFFFFF',
      Measure: '#DAA520',
      MeasureForeground: '#FFFFFF'
    },
    DataColorPalette: {
      Colors: [
        '#FFD700', // Gold - primary bar color
        '#FFA500', // Orange - secondary bar color
        '#FF8C00', // Dark Orange
        '#FFFF00', // Yellow
        '#F0E68C', // Khaki
        '#DAA520', // Goldenrod
        '#B8860B', // Dark Goldenrod
        '#CD853F'  // Peru
      ],
      MinMaxGradient: ['#FFFACD', '#FFD700'],
      EmptyFillColor: '#FFFFFF'
    },
    Typography: {
      FontFamilies: [
        { FontFamily: 'Amazon Ember, Arial, sans-serif' }
      ]
    }
  } as ThemeConfiguration,

  blue: {
    UIColorPalette: {
      PrimaryForeground: '#FFFFFF',
      PrimaryBackground: '#146EB4',
      SecondaryForeground: '#FFFFFF',
      SecondaryBackground: '#232F3E',
      Accent: '#FF9900',
      AccentForeground: '#FFFFFF',
      Danger: '#DC3545',
      DangerForeground: '#FFFFFF',
      Warning: '#FFC107',
      WarningForeground: '#000000',
      Success: '#28A745',
      SuccessForeground: '#FFFFFF',
      Dimension: '#1E3A5F',
      DimensionForeground: '#FFFFFF',
      Measure: '#4A90E2',
      MeasureForeground: '#FFFFFF'
    },
    DataColorPalette: {
      Colors: [
        '#146EB4', // AWS Blue - primary bar color
        '#232F3E', // AWS Dark - secondary bar color
        '#FF9900', // AWS Orange
        '#4A90E2', // Light Blue
        '#1E3A5F', // Dark Blue
        '#87CEEB', // Sky Blue
        '#5DADE2', // Light Steel Blue
        '#3498DB'  // Dodger Blue
      ],
      MinMaxGradient: ['#E3F2FD', '#146EB4'],
      EmptyFillColor: '#FFFFFF'
    },
    Typography: {
      FontFamilies: [
        { FontFamily: 'Amazon Ember, Arial, sans-serif' }
      ]
    }
  } as ThemeConfiguration,

  green: {
    UIColorPalette: {
      PrimaryForeground: '#FFFFFF',
      PrimaryBackground: '#28A745',
      SecondaryForeground: '#FFFFFF',
      SecondaryBackground: '#20C997',
      Accent: '#17A2B8',
      AccentForeground: '#FFFFFF',
      Danger: '#DC3545',
      DangerForeground: '#FFFFFF',
      Warning: '#FFC107',
      WarningForeground: '#000000',
      Success: '#28A745',
      SuccessForeground: '#FFFFFF',
      Dimension: '#32CD32',
      DimensionForeground: '#FFFFFF',
      Measure: '#40E0D0',
      MeasureForeground: '#000000'
    },
    DataColorPalette: {
      Colors: [
        '#28A745', // Success Green - primary bar color
        '#20C997', // Teal - secondary bar color
        '#17A2B8', // Info Blue
        '#6F42C1', // Purple
        '#40E0D0', // Turquoise
        '#32CD32', // Lime Green
        '#98FB98', // Pale Green
        '#00FA9A'  // Medium Spring Green
      ],
      MinMaxGradient: ['#E8F5E8', '#28A745'],
      EmptyFillColor: '#FFFFFF'
    },
    Typography: {
      FontFamilies: [
        { FontFamily: 'Amazon Ember, Arial, sans-serif' }
      ]
    }
  } as ThemeConfiguration,

  red: {
    UIColorPalette: {
      PrimaryForeground: '#FFFFFF',
      PrimaryBackground: '#DC3545',
      SecondaryForeground: '#FFFFFF',
      SecondaryBackground: '#FD7E14',
      Accent: '#E83E8C',
      AccentForeground: '#FFFFFF',
      Danger: '#DC3545',
      DangerForeground: '#FFFFFF',
      Warning: '#FFC107',
      WarningForeground: '#000000',
      Success: '#28A745',
      SuccessForeground: '#FFFFFF',
      Dimension: '#DC143C',
      DimensionForeground: '#FFFFFF',
      Measure: '#FF6347',
      MeasureForeground: '#FFFFFF'
    },
    DataColorPalette: {
      Colors: [
        '#DC3545', // Danger Red - primary bar color
        '#FD7E14', // Orange - secondary bar color
        '#E83E8C', // Pink
        '#6610F2', // Indigo
        '#FF6347', // Tomato
        '#FF4500', // Orange Red
        '#DC143C', // Crimson
        '#B22222'  // Fire Brick
      ],
      MinMaxGradient: ['#F8D7DA', '#DC3545'],
      EmptyFillColor: '#FFFFFF'
    },
    Typography: {
      FontFamilies: [
        { FontFamily: 'Amazon Ember, Arial, sans-serif' }
      ]
    }
  } as ThemeConfiguration,

  purple: {
    UIColorPalette: {
      PrimaryForeground: '#FFFFFF',
      PrimaryBackground: '#6F42C1',
      SecondaryForeground: '#FFFFFF',
      SecondaryBackground: '#9B59B6',
      Accent: '#E91E63',
      AccentForeground: '#FFFFFF',
      Danger: '#DC3545',
      DangerForeground: '#FFFFFF',
      Warning: '#FFC107',
      WarningForeground: '#000000',
      Success: '#28A745',
      SuccessForeground: '#FFFFFF',
      Dimension: '#8E44AD',
      DimensionForeground: '#FFFFFF',
      Measure: '#9C27B0',
      MeasureForeground: '#FFFFFF'
    },
    DataColorPalette: {
      Colors: [
        '#6F42C1', // Purple - primary bar color
        '#9B59B6', // Medium Purple
        '#E91E63', // Pink
        '#8E44AD', // Dark Purple
        '#9C27B0', // Material Purple
        '#BA68C8', // Light Purple
        '#CE93D8', // Very Light Purple
        '#F3E5F5'  // Pale Purple
      ],
      MinMaxGradient: ['#F3E5F5', '#6F42C1'],
      EmptyFillColor: '#FFFFFF'
    },
    Typography: {
      FontFamilies: [
        { FontFamily: 'Amazon Ember, Arial, sans-serif' }
      ]
    }
  } as ThemeConfiguration,

  orange: {
    UIColorPalette: {
      PrimaryForeground: '#FFFFFF',
      PrimaryBackground: '#FF9900',
      SecondaryForeground: '#FFFFFF',
      SecondaryBackground: '#FF8C00',
      Accent: '#FD7E14',
      AccentForeground: '#FFFFFF',
      Danger: '#DC3545',
      DangerForeground: '#FFFFFF',
      Warning: '#FFC107',
      WarningForeground: '#000000',
      Success: '#28A745',
      SuccessForeground: '#FFFFFF',
      Dimension: '#FF6347',
      DimensionForeground: '#FFFFFF',
      Measure: '#FF4500',
      MeasureForeground: '#FFFFFF'
    },
    DataColorPalette: {
      Colors: [
        '#FF9900', // AWS Orange - primary bar color
        '#FF8C00', // Dark Orange
        '#FD7E14', // Bootstrap Orange
        '#FF6347', // Tomato
        '#FF4500', // Orange Red
        '#FFA500', // Orange
        '#FFB74D', // Light Orange
        '#FFCC80'  // Very Light Orange
      ],
      MinMaxGradient: ['#FFF3E0', '#FF9900'],
      EmptyFillColor: '#FFFFFF'
    },
    Typography: {
      FontFamilies: [
        { FontFamily: 'Amazon Ember, Arial, sans-serif' }
      ]
    }
  } as ThemeConfiguration
};

// Get theme configuration by color scheme name
export function getThemeConfiguration(colorScheme: string): ThemeConfiguration {
  return themeConfigurations[colorScheme as keyof typeof themeConfigurations] || themeConfigurations.blue;
}

// Get theme options for QuickSight embedding
export function getThemeOptions(colorScheme: string): ThemeOptions {
  const themeConfig = getThemeConfiguration(colorScheme);
  
  console.log(`Generated theme configuration for ${colorScheme}:`, themeConfig);
  
  return {
    themeOverride: themeConfig,
    preloadThemes: [
      'arn:aws:quicksight::aws:theme/CLASSIC',
      'arn:aws:quicksight::aws:theme/MIDNIGHT',
      'arn:aws:quicksight::aws:theme/RAINIER'
    ]
  };
}

// Helper function to validate theme configuration
export function validateThemeConfiguration(config: ThemeConfiguration): boolean {
  // Basic validation - ensure at least one color palette is defined
  return !!(config.DataColorPalette?.Colors?.length || config.UIColorPalette);
}

// Helper function to generate theme configuration dynamically
export function generateThemeFromColorPalette(colors: string[], name: string): ThemeConfiguration {
  if (colors.length < 2) {
    throw new Error('At least 2 colors are required for theme generation');
  }

  const primaryColor = colors[0];
  const secondaryColor = colors[1];
  
  return {
    UIColorPalette: {
      PrimaryForeground: '#FFFFFF',
      PrimaryBackground: primaryColor,
      SecondaryForeground: '#FFFFFF',
      SecondaryBackground: secondaryColor,
      Accent: colors[2] || primaryColor,
      AccentForeground: '#FFFFFF'
    },
    DataColorPalette: {
      Colors: colors,
      MinMaxGradient: [colors[colors.length - 1], primaryColor],
      EmptyFillColor: '#FFFFFF'
    },
    Typography: {
      FontFamilies: [
        { FontFamily: 'Amazon Ember, Arial, sans-serif' }
      ]
    }
  };
}