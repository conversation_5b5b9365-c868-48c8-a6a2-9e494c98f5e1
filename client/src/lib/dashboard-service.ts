import { createEmbeddingContext } from 'amazon-quicksight-embedding-sdk';
import { dashboardCache } from './dashboard-cache';
import { parseQuickSightParameters } from './quicksight-runtime-filtering';

export interface DashboardConfig {
  dashboardId: string;
  visualId: string;
  accountId: string;
  directoryAlias: string;
  colorScheme?: string;
}

export interface EmbedOptions {
  container: HTMLElement;
  height?: string;
  width?: string;
  onMessage?: (event: any) => void;
}

class DashboardService {
  private embeddingContext: any = null;
  private activeEmbeds = new Map<string, any>();

  async getEmbeddingContext() {
    if (!this.embeddingContext) {
      this.embeddingContext = await createEmbeddingContext();
    }
    return this.embeddingContext;
  }

  async getEmbedUrl(config: DashboardConfig, urlParameters?: Record<string, any>): Promise<string> {
    const params = urlParameters || parseQuickSightParameters();
    
    // Check cache first
    const cachedUrl = dashboardCache.get(config.dashboardId, params);
    if (cachedUrl) {
      console.log('Using cached embed URL');
      return cachedUrl;
    }

    console.log('Fetching new embed URL');
    const response = await fetch('/api/quicksight/embed-url', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        ...config,
        urlParameters: params
      })
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`Failed to get embed URL: ${response.status} - ${errorText}`);
    }

    const { embedUrl } = await response.json();
    
    // Cache the URL
    dashboardCache.set(config.dashboardId, params, embedUrl);
    
    return embedUrl;
  }

  async embedDashboard(
    config: DashboardConfig, 
    options: EmbedOptions,
    urlParameters?: Record<string, any>
  ): Promise<any> {
    const embedUrl = await this.getEmbedUrl(config, urlParameters);
    const context = await this.getEmbeddingContext();

    const embeddedDashboard = await context.embedDashboard(
      {
        url: embedUrl,
        container: options.container,
        height: options.height || "600px",
        width: options.width || "100%",
        withIframePlaceholder: true
      },
      {
        onMessage: options.onMessage
      }
    );

    // Track active embeds for cleanup
    const embedId = `${config.dashboardId}-${Date.now()}`;
    this.activeEmbeds.set(embedId, embeddedDashboard);

    return {
      dashboard: embeddedDashboard,
      embedId,
      cleanup: () => this.cleanup(embedId)
    };
  }

  async cleanup(embedId?: string) {
    if (embedId) {
      const embed = this.activeEmbeds.get(embedId);
      if (embed) {
        await embed.close?.();
        this.activeEmbeds.delete(embedId);
      }
    } else {
      // Cleanup all active embeds
      for (const [id, embed] of this.activeEmbeds.entries()) {
        await embed.close?.();
        this.activeEmbeds.delete(id);
      }
    }
  }

  async applyParameters(embedId: string, parameters: Record<string, any>) {
    const embed = this.activeEmbeds.get(embedId);
    if (!embed) return;

    const formattedParams = Object.entries(parameters).map(([name, value]) => ({
      Name: name,
      Values: Array.isArray(value) ? value : [value]
    }));

    try {
      await embed.setParameters?.(formattedParams);
    } catch (error) {
      console.warn('Failed to apply parameters:', error);
    }
  }
}

export const dashboardService = new DashboardService();