# Technology Stack

## Frontend
- **React 18** with TypeScript
- **Vite** for build tooling and development server
- **Tailwind CSS** with custom design system
- **Wouter** for client-side routing
- **TanStack Query** for server state management
- **Radix UI** components with shadcn/ui styling
- **Amazon QuickSight Embedding SDK** for dashboard integration

## Backend
- **Node.js** with Express server
- **TypeScript** throughout the stack
- **Drizzle ORM** with PostgreSQL (Neon serverless)
- **AWS SDK** for QuickSight integration
- **Express Session** with Passport.js for authentication

## Build System & Development

### Common Commands
```bash
# Development
npm run dev          # Start development server with hot reload

# Production Build
npm run build        # Build client and server for production
npm start           # Start production server

# Database
npm run db:push     # Push schema changes to database

# Type Checking
npm run check       # Run TypeScript compiler check
```

### Development Setup
- Uses Vite dev server with React Fast Refresh
- Express server runs on development mode with tsx
- Environment variables loaded via dotenv
- Replit integration with cartographer plugin

### Production Build
- Client built with Vite to `dist/public`
- Server bundled with esbuild to `dist/index.js`
- Static file serving in production mode
- Single port deployment (PORT env variable, defaults to 5000)

## Key Dependencies
- `amazon-quicksight-embedding-sdk`: Dashboard embedding
- `aws-sdk`: AWS service integration
- `drizzle-orm`: Database ORM
- `@neondatabase/serverless`: PostgreSQL connection
- `zod`: Runtime type validation