# Project Structure

## Root Level Organization
```
├── client/          # React frontend application
├── server/          # Express backend server
├── shared/          # Shared types and schemas
├── dist/            # Production build output
├── logs/            # Application logs
└── attached_assets/ # Static assets and documentation
```

## Client Structure (`client/`)
```
client/
├── src/
│   ├── components/ui/    # Reusable UI components (shadcn/ui)
│   ├── hooks/           # Custom React hooks
│   ├── lib/             # Utility libraries and configurations
│   ├── pages/           # Route components
│   ├── App.tsx          # Main application component
│   ├── main.tsx         # Application entry point
│   └── index.css        # Global styles
└── index.html           # HTML template
```

## Server Structure (`server/`)
```
server/
├── index.ts             # Server entry point and setup
├── routes.ts            # API route definitions
├── vite.ts              # Vite integration for development
├── storage.ts           # Database connection and utilities
└── dataset-inspector.ts # QuickSight dataset utilities
```

## Key Conventions

### Import Aliases
- `@/*` → `client/src/*` (frontend components and utilities)
- `@shared/*` → `shared/*` (shared types and schemas)
- `@assets/*` → `attached_assets/*` (static assets)

### File Naming
- React components: PascalCase (e.g., `Dashboard.tsx`)
- Utilities and configs: kebab-case (e.g., `quicksight-config.ts`)
- API routes: RESTful patterns under `/api/` prefix

### Component Organization
- UI components in `client/src/components/ui/` follow shadcn/ui patterns
- Page components in `client/src/pages/` represent routes
- Custom hooks in `client/src/hooks/` with `use-` prefix
- Library utilities in `client/src/lib/` for specific domains

### QuickSight Integration Files
- `quicksight-config.ts`: Dashboard and account configuration
- `quicksight-runtime-theming.ts`: Theme configuration logic
- `quicksight-runtime-filtering.ts`: URL parameter parsing
- `quicksight-visual-sdk.tsx`: SDK integration utilities

### Environment & Configuration
- `.env` for environment variables
- `components.json` for shadcn/ui configuration
- `tailwind.config.ts` for styling system
- `drizzle.config.ts` for database configuration