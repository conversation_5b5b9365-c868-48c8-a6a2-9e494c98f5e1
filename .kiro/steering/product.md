# Product Overview

This is a QuickSight Dashboard Embedding application that provides runtime theming and URL parameter filtering capabilities for AWS QuickSight dashboards.

## Key Features

- **Runtime Theme Switching**: Dynamic color theme application to embedded QuickSight dashboards
- **URL Parameter Filtering**: Apply dashboard filters through URL hash parameters (e.g., `#p.state=Texas&p.year=2023`)
- **Real-time Dashboard Embedding**: Uses Amazon QuickSight Embedding SDK for seamless dashboard integration
- **Interactive Controls**: Theme selection UI and filter management interface

## Target Use Case

Built for organizations using AWS QuickSight who need:
- Customizable dashboard themes that can be changed without redeployment
- URL-based filtering for shareable dashboard links with pre-applied filters
- Embedded dashboard experience within a custom web application

## AWS Integration

- Connects to QuickSight account ID: ************
- Uses directory alias: ARTBA2025
- Embeds specific dashboard and visual IDs for data visualization