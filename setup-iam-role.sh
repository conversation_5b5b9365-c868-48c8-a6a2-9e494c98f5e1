#!/bin/bash

# Create IAM role for QuickSight embedding
echo "Creating QuickSight IAM role..."

# Create trust policy file
cat > trust-policy.json << EOF
{
    "Version": "2012-10-17",
    "Statement": [
        {
            "Effect": "Allow",
            "Principal": {
                "AWS": "arn:aws:iam::288552100418:root"
            },
            "Action": "sts:AssumeRole"
        }
    ]
}
EOF

# Create permissions policy file
cat > quicksight-policy.json << EOF
{
    "Version": "2012-10-17",
    "Statement": [
        {
            "Effect": "Allow",
            "Action": [
                "quicksight:RegisterUser",
                "quicksight:GetDashboardEmbedUrl",
                "quicksight:GetAuthCode",
                "quicksight:GetSessionEmbedUrl",
                "quicksight:GenerateEmbedUrlForRegisteredUser"
            ],
            "Resource": "*"
        }
    ]
}
EOF

# Create the IAM role
aws iam create-role \
    --role-name QuickSightEmbeddingRole \
    --assume-role-policy-document file://trust-policy.json

# Create and attach the policy
aws iam create-policy \
    --policy-name QuickSightEmbeddingPolicy \
    --policy-document file://quicksight-policy.json

aws iam attach-role-policy \
    --role-name QuickSightEmbeddingRole \
    --policy-arn arn:aws:iam::288552100418:policy/QuickSightEmbeddingPolicy

echo "IAM role created successfully!"
echo "Role ARN: arn:aws:iam::288552100418:role/QuickSightEmbeddingRole"

# Cleanup
rm trust-policy.json quicksight-policy.json