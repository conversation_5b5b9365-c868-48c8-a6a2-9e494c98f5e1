[debug] [2025-07-18T01:55:43.229Z] Checked if tokens are valid: true, expires at: 1752806116995
[debug] [2025-07-18T01:55:43.230Z] Checked if tokens are valid: true, expires at: 1752806116995
[debug] [2025-07-18T01:55:43.230Z] Checked if tokens are valid: true, expires at: 1752806116995
[debug] [2025-07-18T01:55:43.231Z] Checked if tokens are valid: true, expires at: 1752806116995
[debug] [2025-07-18T01:55:43.231Z] >>> [apiv2][query] GET https://serviceusage.googleapis.com/v1/projects/po2vf2ae7tal9invaj7jkf4a06hsac/services/firebasecrashlytics.googleapis.com [none]
[debug] [2025-07-18T01:55:43.231Z] >>> [apiv2][(partial)header] GET https://serviceusage.googleapis.com/v1/projects/po2vf2ae7tal9invaj7jkf4a06hsac/services/firebasecrashlytics.googleapis.com x-goog-quota-user=projects/po2vf2ae7tal9invaj7jkf4a06hsac
[debug] [2025-07-18T01:55:43.240Z] >>> [apiv2][query] GET https://serviceusage.googleapis.com/v1/projects/po2vf2ae7tal9invaj7jkf4a06hsac/services/firebaseapphosting.googleapis.com [none]
[debug] [2025-07-18T01:55:43.240Z] >>> [apiv2][(partial)header] GET https://serviceusage.googleapis.com/v1/projects/po2vf2ae7tal9invaj7jkf4a06hsac/services/firebaseapphosting.googleapis.com x-goog-quota-user=projects/po2vf2ae7tal9invaj7jkf4a06hsac
[debug] [2025-07-18T01:55:43.282Z] Checked if tokens are valid: true, expires at: 1752806116995
[debug] [2025-07-18T01:55:43.282Z] Checked if tokens are valid: true, expires at: 1752806116995
[debug] [2025-07-18T01:55:43.282Z] Checked if tokens are valid: true, expires at: 1752806116995
[debug] [2025-07-18T01:55:43.282Z] Checked if tokens are valid: true, expires at: 1752806116995
[debug] [2025-07-18T01:55:43.282Z] >>> [apiv2][query] GET https://serviceusage.googleapis.com/v1/projects/po2vf2ae7tal9invaj7jkf4a06hsac/services/firebasecrashlytics.googleapis.com [none]
[debug] [2025-07-18T01:55:43.282Z] >>> [apiv2][(partial)header] GET https://serviceusage.googleapis.com/v1/projects/po2vf2ae7tal9invaj7jkf4a06hsac/services/firebasecrashlytics.googleapis.com x-goog-quota-user=projects/po2vf2ae7tal9invaj7jkf4a06hsac
[debug] [2025-07-18T01:55:43.283Z] >>> [apiv2][query] GET https://serviceusage.googleapis.com/v1/projects/po2vf2ae7tal9invaj7jkf4a06hsac/services/firebaseapphosting.googleapis.com [none]
[debug] [2025-07-18T01:55:43.283Z] >>> [apiv2][(partial)header] GET https://serviceusage.googleapis.com/v1/projects/po2vf2ae7tal9invaj7jkf4a06hsac/services/firebaseapphosting.googleapis.com x-goog-quota-user=projects/po2vf2ae7tal9invaj7jkf4a06hsac
[debug] [2025-07-18T01:55:43.521Z] <<< [apiv2][status] GET https://serviceusage.googleapis.com/v1/projects/po2vf2ae7tal9invaj7jkf4a06hsac/services/firebaseapphosting.googleapis.com 200
[debug] [2025-07-18T01:55:43.521Z] <<< [apiv2][body] GET https://serviceusage.googleapis.com/v1/projects/po2vf2ae7tal9invaj7jkf4a06hsac/services/firebaseapphosting.googleapis.com [omitted]
[debug] [2025-07-18T01:55:43.522Z] <<< [apiv2][status] GET https://serviceusage.googleapis.com/v1/projects/po2vf2ae7tal9invaj7jkf4a06hsac/services/firebasecrashlytics.googleapis.com 200
[debug] [2025-07-18T01:55:43.522Z] <<< [apiv2][body] GET https://serviceusage.googleapis.com/v1/projects/po2vf2ae7tal9invaj7jkf4a06hsac/services/firebasecrashlytics.googleapis.com [omitted]
[debug] [2025-07-18T01:55:43.576Z] <<< [apiv2][status] GET https://serviceusage.googleapis.com/v1/projects/po2vf2ae7tal9invaj7jkf4a06hsac/services/firebaseapphosting.googleapis.com 200
[debug] [2025-07-18T01:55:43.576Z] <<< [apiv2][body] GET https://serviceusage.googleapis.com/v1/projects/po2vf2ae7tal9invaj7jkf4a06hsac/services/firebaseapphosting.googleapis.com [omitted]
[debug] [2025-07-18T01:55:43.624Z] <<< [apiv2][status] GET https://serviceusage.googleapis.com/v1/projects/po2vf2ae7tal9invaj7jkf4a06hsac/services/firebasecrashlytics.googleapis.com 200
[debug] [2025-07-18T01:55:43.624Z] <<< [apiv2][body] GET https://serviceusage.googleapis.com/v1/projects/po2vf2ae7tal9invaj7jkf4a06hsac/services/firebasecrashlytics.googleapis.com [omitted]
[debug] [2025-07-18T01:55:43.646Z] > command requires scopes: ["email","openid","https://www.googleapis.com/auth/cloudplatformprojects.readonly","https://www.googleapis.com/auth/firebase","https://www.googleapis.com/auth/cloud-platform"]
[debug] [2025-07-18T01:55:43.646Z] > authorizing via signed-in user (<EMAIL>)
[debug] [2025-07-18T01:57:48.165Z] Checked if tokens are valid: true, expires at: 1752806116995
[debug] [2025-07-18T01:57:48.167Z] Checked if tokens are valid: true, expires at: 1752806116995
[debug] [2025-07-18T01:57:48.167Z] Checked if tokens are valid: true, expires at: 1752806116995
[debug] [2025-07-18T01:57:48.167Z] Checked if tokens are valid: true, expires at: 1752806116995
[debug] [2025-07-18T01:57:48.168Z] >>> [apiv2][query] GET https://serviceusage.googleapis.com/v1/projects/po2vf2ae7tal9invaj7jkf4a06hsac/services/firebasecrashlytics.googleapis.com [none]
[debug] [2025-07-18T01:57:48.168Z] >>> [apiv2][(partial)header] GET https://serviceusage.googleapis.com/v1/projects/po2vf2ae7tal9invaj7jkf4a06hsac/services/firebasecrashlytics.googleapis.com x-goog-quota-user=projects/po2vf2ae7tal9invaj7jkf4a06hsac
[debug] [2025-07-18T01:57:48.177Z] >>> [apiv2][query] GET https://serviceusage.googleapis.com/v1/projects/po2vf2ae7tal9invaj7jkf4a06hsac/services/firebaseapphosting.googleapis.com [none]
[debug] [2025-07-18T01:57:48.177Z] >>> [apiv2][(partial)header] GET https://serviceusage.googleapis.com/v1/projects/po2vf2ae7tal9invaj7jkf4a06hsac/services/firebaseapphosting.googleapis.com x-goog-quota-user=projects/po2vf2ae7tal9invaj7jkf4a06hsac
[debug] [2025-07-18T01:57:48.184Z] Checked if tokens are valid: true, expires at: 1752806116995
[debug] [2025-07-18T01:57:48.184Z] Checked if tokens are valid: true, expires at: 1752806116995
[debug] [2025-07-18T01:57:48.184Z] Checked if tokens are valid: true, expires at: 1752806116995
[debug] [2025-07-18T01:57:48.184Z] Checked if tokens are valid: true, expires at: 1752806116995
[debug] [2025-07-18T01:57:48.184Z] >>> [apiv2][query] GET https://serviceusage.googleapis.com/v1/projects/po2vf2ae7tal9invaj7jkf4a06hsac/services/firebasecrashlytics.googleapis.com [none]
[debug] [2025-07-18T01:57:48.184Z] >>> [apiv2][(partial)header] GET https://serviceusage.googleapis.com/v1/projects/po2vf2ae7tal9invaj7jkf4a06hsac/services/firebasecrashlytics.googleapis.com x-goog-quota-user=projects/po2vf2ae7tal9invaj7jkf4a06hsac
[debug] [2025-07-18T01:57:48.184Z] >>> [apiv2][query] GET https://serviceusage.googleapis.com/v1/projects/po2vf2ae7tal9invaj7jkf4a06hsac/services/firebaseapphosting.googleapis.com [none]
[debug] [2025-07-18T01:57:48.184Z] >>> [apiv2][(partial)header] GET https://serviceusage.googleapis.com/v1/projects/po2vf2ae7tal9invaj7jkf4a06hsac/services/firebaseapphosting.googleapis.com x-goog-quota-user=projects/po2vf2ae7tal9invaj7jkf4a06hsac
[debug] [2025-07-18T01:57:48.548Z] <<< [apiv2][status] GET https://serviceusage.googleapis.com/v1/projects/po2vf2ae7tal9invaj7jkf4a06hsac/services/firebasecrashlytics.googleapis.com 200
[debug] [2025-07-18T01:57:48.549Z] <<< [apiv2][body] GET https://serviceusage.googleapis.com/v1/projects/po2vf2ae7tal9invaj7jkf4a06hsac/services/firebasecrashlytics.googleapis.com [omitted]
[debug] [2025-07-18T01:57:48.553Z] <<< [apiv2][status] GET https://serviceusage.googleapis.com/v1/projects/po2vf2ae7tal9invaj7jkf4a06hsac/services/firebaseapphosting.googleapis.com 200
[debug] [2025-07-18T01:57:48.553Z] <<< [apiv2][body] GET https://serviceusage.googleapis.com/v1/projects/po2vf2ae7tal9invaj7jkf4a06hsac/services/firebaseapphosting.googleapis.com [omitted]
[debug] [2025-07-18T01:57:48.554Z] <<< [apiv2][status] GET https://serviceusage.googleapis.com/v1/projects/po2vf2ae7tal9invaj7jkf4a06hsac/services/firebasecrashlytics.googleapis.com 200
[debug] [2025-07-18T01:57:48.554Z] <<< [apiv2][body] GET https://serviceusage.googleapis.com/v1/projects/po2vf2ae7tal9invaj7jkf4a06hsac/services/firebasecrashlytics.googleapis.com [omitted]
[debug] [2025-07-18T01:57:48.575Z] > command requires scopes: ["email","openid","https://www.googleapis.com/auth/cloudplatformprojects.readonly","https://www.googleapis.com/auth/firebase","https://www.googleapis.com/auth/cloud-platform"]
[debug] [2025-07-18T01:57:48.576Z] > authorizing via signed-in user (<EMAIL>)
[debug] [2025-07-18T01:57:48.831Z] <<< [apiv2][status] GET https://serviceusage.googleapis.com/v1/projects/po2vf2ae7tal9invaj7jkf4a06hsac/services/firebaseapphosting.googleapis.com 200
[debug] [2025-07-18T01:57:48.831Z] <<< [apiv2][body] GET https://serviceusage.googleapis.com/v1/projects/po2vf2ae7tal9invaj7jkf4a06hsac/services/firebaseapphosting.googleapis.com [omitted]
[debug] [2025-07-18T01:59:05.007Z] Checked if tokens are valid: true, expires at: 1752806116995
[debug] [2025-07-18T01:59:05.009Z] Checked if tokens are valid: true, expires at: 1752806116995
[debug] [2025-07-18T01:59:05.009Z] Checked if tokens are valid: true, expires at: 1752806116995
[debug] [2025-07-18T01:59:05.009Z] Checked if tokens are valid: true, expires at: 1752806116995
[debug] [2025-07-18T01:59:05.009Z] >>> [apiv2][query] GET https://serviceusage.googleapis.com/v1/projects/po2vf2ae7tal9invaj7jkf4a06hsac/services/firebasecrashlytics.googleapis.com [none]
[debug] [2025-07-18T01:59:05.009Z] >>> [apiv2][(partial)header] GET https://serviceusage.googleapis.com/v1/projects/po2vf2ae7tal9invaj7jkf4a06hsac/services/firebasecrashlytics.googleapis.com x-goog-quota-user=projects/po2vf2ae7tal9invaj7jkf4a06hsac
[debug] [2025-07-18T01:59:05.018Z] >>> [apiv2][query] GET https://serviceusage.googleapis.com/v1/projects/po2vf2ae7tal9invaj7jkf4a06hsac/services/firebaseapphosting.googleapis.com [none]
[debug] [2025-07-18T01:59:05.018Z] >>> [apiv2][(partial)header] GET https://serviceusage.googleapis.com/v1/projects/po2vf2ae7tal9invaj7jkf4a06hsac/services/firebaseapphosting.googleapis.com x-goog-quota-user=projects/po2vf2ae7tal9invaj7jkf4a06hsac
[debug] [2025-07-18T01:59:05.025Z] Checked if tokens are valid: true, expires at: 1752806116995
[debug] [2025-07-18T01:59:05.025Z] Checked if tokens are valid: true, expires at: 1752806116995
[debug] [2025-07-18T01:59:05.025Z] Checked if tokens are valid: true, expires at: 1752806116995
[debug] [2025-07-18T01:59:05.025Z] Checked if tokens are valid: true, expires at: 1752806116995
[debug] [2025-07-18T01:59:05.025Z] >>> [apiv2][query] GET https://serviceusage.googleapis.com/v1/projects/po2vf2ae7tal9invaj7jkf4a06hsac/services/firebasecrashlytics.googleapis.com [none]
[debug] [2025-07-18T01:59:05.025Z] >>> [apiv2][(partial)header] GET https://serviceusage.googleapis.com/v1/projects/po2vf2ae7tal9invaj7jkf4a06hsac/services/firebasecrashlytics.googleapis.com x-goog-quota-user=projects/po2vf2ae7tal9invaj7jkf4a06hsac
[debug] [2025-07-18T01:59:05.026Z] >>> [apiv2][query] GET https://serviceusage.googleapis.com/v1/projects/po2vf2ae7tal9invaj7jkf4a06hsac/services/firebaseapphosting.googleapis.com [none]
[debug] [2025-07-18T01:59:05.026Z] >>> [apiv2][(partial)header] GET https://serviceusage.googleapis.com/v1/projects/po2vf2ae7tal9invaj7jkf4a06hsac/services/firebaseapphosting.googleapis.com x-goog-quota-user=projects/po2vf2ae7tal9invaj7jkf4a06hsac
[debug] [2025-07-18T01:59:05.280Z] <<< [apiv2][status] GET https://serviceusage.googleapis.com/v1/projects/po2vf2ae7tal9invaj7jkf4a06hsac/services/firebasecrashlytics.googleapis.com 200
[debug] [2025-07-18T01:59:05.281Z] <<< [apiv2][body] GET https://serviceusage.googleapis.com/v1/projects/po2vf2ae7tal9invaj7jkf4a06hsac/services/firebasecrashlytics.googleapis.com [omitted]
[debug] [2025-07-18T01:59:05.360Z] <<< [apiv2][status] GET https://serviceusage.googleapis.com/v1/projects/po2vf2ae7tal9invaj7jkf4a06hsac/services/firebasecrashlytics.googleapis.com 200
[debug] [2025-07-18T01:59:05.360Z] <<< [apiv2][body] GET https://serviceusage.googleapis.com/v1/projects/po2vf2ae7tal9invaj7jkf4a06hsac/services/firebasecrashlytics.googleapis.com [omitted]
[debug] [2025-07-18T01:59:05.364Z] <<< [apiv2][status] GET https://serviceusage.googleapis.com/v1/projects/po2vf2ae7tal9invaj7jkf4a06hsac/services/firebaseapphosting.googleapis.com 200
[debug] [2025-07-18T01:59:05.365Z] <<< [apiv2][body] GET https://serviceusage.googleapis.com/v1/projects/po2vf2ae7tal9invaj7jkf4a06hsac/services/firebaseapphosting.googleapis.com [omitted]
[debug] [2025-07-18T01:59:05.370Z] <<< [apiv2][status] GET https://serviceusage.googleapis.com/v1/projects/po2vf2ae7tal9invaj7jkf4a06hsac/services/firebaseapphosting.googleapis.com 200
[debug] [2025-07-18T01:59:05.370Z] <<< [apiv2][body] GET https://serviceusage.googleapis.com/v1/projects/po2vf2ae7tal9invaj7jkf4a06hsac/services/firebaseapphosting.googleapis.com [omitted]
[debug] [2025-07-18T01:59:05.386Z] > command requires scopes: ["email","openid","https://www.googleapis.com/auth/cloudplatformprojects.readonly","https://www.googleapis.com/auth/firebase","https://www.googleapis.com/auth/cloud-platform"]
[debug] [2025-07-18T01:59:05.387Z] > authorizing via signed-in user (<EMAIL>)
