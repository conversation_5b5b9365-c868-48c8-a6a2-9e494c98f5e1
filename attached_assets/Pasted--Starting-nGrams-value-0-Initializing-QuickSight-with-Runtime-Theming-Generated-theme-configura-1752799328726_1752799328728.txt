 Starting nGrams value 0
 Initializing QuickSight with Runtime Theming...
 Generated theme configuration for yellow: Object
 Applying theme options: Object
 Parsed filters from URL: Array(0)
 Applying runtime filters: Array(0)
 Filter JSON for debugging: []
 Embedding with theme options: Object
 QuickSight embedded with runtime theming and filtering successfully
 Dashboard ready for diagnostics: e14
 Runtime theme and filter configuration applied successfully
 Generated theme configuration for yellow: Object
 Applying runtime theme override for yellow scheme
Failed to execute 'postMessage' on 'DOMWindow': The target origin provided ('<URL>') does not match the recipient window's origin ('<URL>').
Failed to execute 'postMessage' on 'DOMWindow': The target origin provided ('<URL>') does not match the recipient window's origin ('<URL>').
Failed to execute 'postMessage' on 'DOMWindow': The target origin provided ('<URL>') does not match the recipient window's origin ('<URL>').
Failed to execute 'postMessage' on 'DOMWindow': The target origin provided ('<URL>') does not match the recipient window's origin ('<URL>').
Failed to execute 'postMessage' on 'DOMWindow': The target origin provided ('<URL>') does not match the recipient window's origin ('<URL>').
Failed to execute 'postMessage' on 'DOMWindow': The target origin provided ('<URL>') does not match the recipient window's origin ('<URL>').
Failed to execute 'postMessage' on 'DOMWindow': The target origin provided ('<URL>') does not match the recipient window's origin ('<URL>').
Failed to execute 'postMessage' on 'DOMWindow': The target origin provided ('<URL>') does not match the recipient window's origin ('<URL>').
Failed to execute 'postMessage' on 'DOMWindow': The target origin provided ('<URL>') does not match the recipient window's origin ('<URL>').
Failed to execute 'postMessage' on 'DOMWindow': The target origin provided ('<URL>') does not match the recipient window's origin ('<URL>').
Failed to execute 'postMessage' on 'DOMWindow': The target origin provided ('<URL>') does not match the recipient window's origin ('<URL>').
Failed to execute 'postMessage' on 'DOMWindow': The target origin provided ('<URL>') does not match the recipient window's origin ('<URL>').
Failed to execute 'postMessage' on 'DOMWindow': The target origin provided ('<URL>') does not match the recipient window's origin ('<URL>').
 Applying filters to existing dashboard: Array(0)
 Clearing all filters...
 ❌ Failed to apply filters to existing dashboard: 
overrideMethod @ installHook.js:1
 Trying URL-based filter approach - will re-initialize with filtered embed URL...
 [ECharts] DEPRECATED: 'normal' hierarchy in itemStyle has been removed since 4.0. All style properties are configured in itemStyle directly now.
Io @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
 [ECharts] DEPRECATED: textStyle hierarchy in axisLabel has been removed since 4.0. All textStyle properties are configured in axisLabel directly now.
Io @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
 Initializing QuickSight with Runtime Theming...
 Generated theme configuration for yellow: Object
 Applying theme options: Object
 Parsed filters from URL: Array(0)
 Applying runtime filters: Array(0)
 Filter JSON for debugging: []
 Embedding with theme options: Object
 QuickSight embedded with runtime theming and filtering successfully
 Dashboard ready for diagnostics: e14
 Runtime theme and filter configuration applied successfully
 Generated theme configuration for yellow: Object
 Applying runtime theme override for yellow scheme
 Message with unrecognized event target received
overrideMethod @ installHook.js:1
 Applying filters to existing dashboard: Array(0)
 Clearing all filters...
 ❌ Failed to apply filters to existing dashboard: 
overrideMethod @ installHook.js:1
 Trying URL-based filter approach - will re-initialize with filtered embed URL...
 Message with unrecognized event target received
overrideMethod @ installHook.js:1
 Message with unrecognized event target received
overrideMethod @ installHook.js:1
 Message with unrecognized event target received
overrideMethod @ installHook.js:1
 Message with unrecognized event target received
overrideMethod @ installHook.js:1
 Message with unrecognized event target received
overrideMethod @ installHook.js:1
 [ECharts] DEPRECATED: 'normal' hierarchy in itemStyle has been removed since 4.0. All style properties are configured in itemStyle directly now.
Io @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
 [ECharts] DEPRECATED: textStyle hierarchy in axisLabel has been removed since 4.0. All textStyle properties are configured in axisLabel directly now.
Io @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
 Initializing QuickSight with Runtime Theming...
 Generated theme configuration for yellow: Object
 Applying theme options: Object
 Parsed filters from URL: Array(0)
 Applying runtime filters: Array(0)
 Filter JSON for debugging: []
 Embedding with theme options: Object
 QuickSight embedded with runtime theming and filtering successfully
 Dashboard ready for diagnostics: e14
 Runtime theme and filter configuration applied successfully
 Generated theme configuration for yellow: Object
 Applying runtime theme override for yellow scheme
 Message with unrecognized event target received
overrideMethod @ installHook.js:1
 Message with unrecognized event target received
overrideMethod @ installHook.js:1
 Applying filters to existing dashboard: Array(0)
 Clearing all filters...
 ❌ Failed to apply filters to existing dashboard: 
overrideMethod @ installHook.js:1
 Trying URL-based filter approach - will re-initialize with filtered embed URL...
 Message with unrecognized event target received
overrideMethod @ installHook.js:1
 Message with unrecognized event target received
overrideMethod @ installHook.js:1
 Message with unrecognized event target received
overrideMethod @ installHook.js:1
 Message with unrecognized event target received
overrideMethod @ installHook.js:1
 Message with unrecognized event target received
overrideMethod @ installHook.js:1
 Message with unrecognized event target received
overrideMethod @ installHook.js:1
 Message with unrecognized event target received
overrideMethod @ installHook.js:1
 Message with unrecognized event target received
overrideMethod @ installHook.js:1
 Message with unrecognized event target received
overrideMethod @ installHook.js:1
 Message with unrecognized event target received
overrideMethod @ installHook.js:1
 Failed to apply runtime theme: SET_THEME_OVERRIDE timed out
overrideMethod @ installHook.js:1
 [ECharts] DEPRECATED: 'normal' hierarchy in itemStyle has been removed since 4.0. All style properties are configured in itemStyle directly now.
Io @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
 [ECharts] DEPRECATED: textStyle hierarchy in axisLabel has been removed since 4.0. All textStyle properties are configured in axisLabel directly now.
Io @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
 [ECharts] Can't get DOM width or height. Please check dom.clientWidth and dom.clientHeight. They should not be 0.For example, you may need to call this in the callback of window.onload.
Io @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
 Initializing QuickSight with Runtime Theming...
 Generated theme configuration for yellow: Object
 Applying theme options: Object
 Parsed filters from URL: Array(0)
 Applying runtime filters: Array(0)
 Filter JSON for debugging: []
 Embedding with theme options: Object
 QuickSight embedded with runtime theming and filtering successfully
 Dashboard ready for diagnostics: e14
 Runtime theme and filter configuration applied successfully
 Generated theme configuration for yellow: Object
 Applying runtime theme override for yellow scheme
 Message with unrecognized event target received
overrideMethod @ installHook.js:1
 Message with unrecognized event target received
overrideMethod @ installHook.js:1
 Message with unrecognized event target received
overrideMethod @ installHook.js:1
 Applying filters to existing dashboard: Array(0)
 Clearing all filters...
 ❌ Failed to apply filters to existing dashboard: 
overrideMethod @ installHook.js:1
 Trying URL-based filter approach - will re-initialize with filtered embed URL...
 Message with unrecognized event target received
overrideMethod @ installHook.js:1
 Message with unrecognized event target received
overrideMethod @ installHook.js:1
 Message with unrecognized event target received
overrideMethod @ installHook.js:1
 Message with unrecognized event target received
overrideMethod @ installHook.js:1
 Message with unrecognized event target received
overrideMethod @ installHook.js:1
 Message with unrecognized event target received
overrideMethod @ installHook.js:1
 Message with unrecognized event target received
overrideMethod @ installHook.js:1
 Message with unrecognized event target received
overrideMethod @ installHook.js:1
 Message with unrecognized event target received
overrideMethod @ installHook.js:1
 Failed to apply runtime theme: SET_THEME_OVERRIDE timed out
overrideMethod @ installHook.js:1
 Message with unrecognized event target received
overrideMethod @ installHook.js:1
 Message with unrecognized event target received
overrideMethod @ installHook.js:1
 Message with unrecognized event target received
overrideMethod @ installHook.js:1
 Message with unrecognized event target received
overrideMethod @ installHook.js:1
 Message with unrecognized event target received
overrideMethod @ installHook.js:1
 Message with unrecognized event target received
overrideMethod @ installHook.js:1
 [ECharts] DEPRECATED: 'normal' hierarchy in itemStyle has been removed since 4.0. All style properties are configured in itemStyle directly now.
Io @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
 [ECharts] DEPRECATED: textStyle hierarchy in axisLabel has been removed since 4.0. All textStyle properties are configured in axisLabel directly now.
Io @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
 Initializing QuickSight with Runtime Theming...
 Generated theme configuration for yellow: Object
 Applying theme options: Object
 Parsed filters from URL: Array(0)
 Applying runtime filters: Array(0)
 Filter JSON for debugging: []
 Embedding with theme options: Object
 QuickSight embedded with runtime theming and filtering successfully
 Dashboard ready for diagnostics: e14
 Runtime theme and filter configuration applied successfully
 Generated theme configuration for yellow: Object
 Applying runtime theme override for yellow scheme
 Message with unrecognized event target received
overrideMethod @ installHook.js:1
 Message with unrecognized event target received
overrideMethod @ installHook.js:1
 Message with unrecognized event target received
overrideMethod @ installHook.js:1
 Message with unrecognized event target received
overrideMethod @ installHook.js:1
 Applying filters to existing dashboard: Array(0)
 Clearing all filters...
 ❌ Failed to apply filters to existing dashboard: 
overrideMethod @ installHook.js:1
 Trying URL-based filter approach - will re-initialize with filtered embed URL...
 Message with unrecognized event target received
overrideMethod @ installHook.js:1
 Message with unrecognized event target received
overrideMethod @ installHook.js:1
 Message with unrecognized event target received
overrideMethod @ installHook.js:1
 Message with unrecognized event target received
overrideMethod @ installHook.js:1
 Message with unrecognized event target received
overrideMethod @ installHook.js:1
 Message with unrecognized event target received
overrideMethod @ installHook.js:1
 Message with unrecognized event target received
overrideMethod @ installHook.js:1
 Message with unrecognized event target received
overrideMethod @ installHook.js:1
 Message with unrecognized event target received
overrideMethod @ installHook.js:1
 Message with unrecognized event target received
overrideMethod @ installHook.js:1
 Message with unrecognized event target received
overrideMethod @ installHook.js:1
 Message with unrecognized event target received
overrideMethod @ installHook.js:1
 Failed to apply runtime theme: SET_THEME_OVERRIDE timed out
overrideMethod @ installHook.js:1
 Message with unrecognized event target received
overrideMethod @ installHook.js:1
 Message with unrecognized event target received
overrideMethod @ installHook.js:1
 Message with unrecognized event target received
overrideMethod @ installHook.js:1
 Message with unrecognized event target received
overrideMethod @ installHook.js:1
 Message with unrecognized event target received
overrideMethod @ installHook.js:1
 Message with unrecognized event target received
overrideMethod @ installHook.js:1
 Message with unrecognized event target received
overrideMethod @ installHook.js:1
 Message with unrecognized event target received
overrideMethod @ installHook.js:1
 [ECharts] DEPRECATED: 'normal' hierarchy in itemStyle has been removed since 4.0. All style properties are configured in itemStyle directly now.
Io @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
 [ECharts] DEPRECATED: textStyle hierarchy in axisLabel has been removed since 4.0. All textStyle properties are configured in axisLabel directly now.
Io @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
 Initializing QuickSight with Runtime Theming...
 Generated theme configuration for yellow: Object
 Applying theme options: Object
 Parsed filters from URL: Array(0)
 Applying runtime filters: Array(0)
 Filter JSON for debugging: []
 Embedding with theme options: Object
 QuickSight embedded with runtime theming and filtering successfully
 Dashboard ready for diagnostics: e14
 Runtime theme and filter configuration applied successfully
 Generated theme configuration for yellow: Object
 Applying runtime theme override for yellow scheme
 Message with unrecognized event target received
overrideMethod @ installHook.js:1
 Message with unrecognized event target received
overrideMethod @ installHook.js:1
 Message with unrecognized event target received
overrideMethod @ installHook.js:1
 Message with unrecognized event target received
overrideMethod @ installHook.js:1
 Message with unrecognized event target received
overrideMethod @ installHook.js:1
 Applying filters to existing dashboard: Array(0)
 Clearing all filters...
 ❌ Failed to apply filters to existing dashboard: 
overrideMethod @ installHook.js:1
 Trying URL-based filter approach - will re-initialize with filtered embed URL...
 Message with unrecognized event target received
overrideMethod @ installHook.js:1
 Message with unrecognized event target received
overrideMethod @ installHook.js:1
 Message with unrecognized event target received
overrideMethod @ installHook.js:1
 Message with unrecognized event target received
overrideMethod @ installHook.js:1
 Message with unrecognized event target received
overrideMethod @ installHook.js:1
 Message with unrecognized event target received
overrideMethod @ installHook.js:1
 Message with unrecognized event target received
overrideMethod @ installHook.js:1
 Message with unrecognized event target received
overrideMethod @ installHook.js:1
 Message with unrecognized event target received
overrideMethod @ installHook.js:1
 Message with unrecognized event target received
overrideMethod @ installHook.js:1
 Message with unrecognized event target received
overrideMethod @ installHook.js:1
 Message with unrecognized event target received
overrideMethod @ installHook.js:1
 Message with unrecognized event target received
overrideMethod @ installHook.js:1
 Message with unrecognized event target received
overrideMethod @ installHook.js:1
 Message with unrecognized event target received
overrideMethod @ installHook.js:1
 Failed to apply runtime theme: SET_THEME_OVERRIDE timed out
overrideMethod @ installHook.js:1
 Message with unrecognized event target received
overrideMethod @ installHook.js:1
 Message with unrecognized event target received
overrideMethod @ installHook.js:1
 Message with unrecognized event target received
overrideMethod @ installHook.js:1
 Message with unrecognized event target received
overrideMethod @ installHook.js:1
 Message with unrecognized event target received
overrideMethod @ installHook.js:1
 Message with unrecognized event target received
overrideMethod @ installHook.js:1
 Message with unrecognized event target received
overrideMethod @ installHook.js:1
 Message with unrecognized event target received
overrideMethod @ installHook.js:1
 Message with unrecognized event target received
overrideMethod @ installHook.js:1
 Message with unrecognized event target received
overrideMethod @ installHook.js:1
 [ECharts] DEPRECATED: 'normal' hierarchy in itemStyle has been removed since 4.0. All style properties are configured in itemStyle directly now.
Io @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
 [ECharts] DEPRECATED: textStyle hierarchy in axisLabel has been removed since 4.0. All textStyle properties are configured in axisLabel directly now.
Io @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
 Initializing QuickSight with Runtime Theming...
 Generated theme configuration for yellow: Object
 Applying theme options: Object
 Parsed filters from URL: Array(0)
 Applying runtime filters: Array(0)
 Filter JSON for debugging: []
 Embedding with theme options: Object
 QuickSight embedded with runtime theming and filtering successfully
 Dashboard ready for diagnostics: e14
 Runtime theme and filter configuration applied successfully
 Generated theme configuration for yellow: Object
 Applying runtime theme override for yellow scheme
 Message with unrecognized event target received
overrideMethod @ installHook.js:1
 Message with unrecognized event target received
overrideMethod @ installHook.js:1
 Message with unrecognized event target received
overrideMethod @ installHook.js:1
 Message with unrecognized event target received
overrideMethod @ installHook.js:1
 Message with unrecognized event target received
overrideMethod @ installHook.js:1
 Message with unrecognized event target received
overrideMethod @ installHook.js:1
quicksight-visual-sdk.tsx:251 Applying filters to existing dashboard: Array(0)
quicksight-visual-sdk.tsx:261 Clearing all filters...
hook.js:608 ❌ Failed to apply filters to existing dashboard: TypeError: embeddedDashboard.setFilters is not a function
    at applyFiltersToExistingDashboard (quicksight-visual-sdk.tsx:262:35)
overrideMethod @ hook.js:608
quicksight-visual-sdk.tsx:271 Trying URL-based filter approach - will re-initialize with filtered embed URL...
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
 [ECharts] DEPRECATED: 'normal' hierarchy in itemStyle has been removed since 4.0. All style properties are configured in itemStyle directly now.
Io @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
 [ECharts] DEPRECATED: textStyle hierarchy in axisLabel has been removed since 4.0. All textStyle properties are configured in axisLabel directly now.
Io @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Failed to apply runtime theme: SET_THEME_OVERRIDE timed out
overrideMethod @ hook.js:608
quicksight-visual-sdk.tsx:56 Initializing QuickSight with Runtime Theming...
quicksight-runtime-theming.ts:128 Generated theme configuration for yellow: Object
quicksight-visual-sdk.tsx:66 Applying theme options: Object
quicksight-runtime-filtering.ts:184 Parsed filters from URL: Array(0)
quicksight-visual-sdk.tsx:72 Applying runtime filters: Array(0)
quicksight-visual-sdk.tsx:73 Filter JSON for debugging: []
quicksight-visual-sdk.tsx:105 Embedding with theme options: Object
quicksight-visual-sdk.tsx:113 QuickSight embedded with runtime theming and filtering successfully
home.tsx:181 Dashboard ready for diagnostics: e14
quicksight-visual-sdk.tsx:160 Runtime theme and filter configuration applied successfully
quicksight-runtime-theming.ts:128 Generated theme configuration for yellow: Object
quicksight-visual-sdk.tsx:180 Applying runtime theme override for yellow scheme
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
quicksight-visual-sdk.tsx:251 Applying filters to existing dashboard: Array(0)
quicksight-visual-sdk.tsx:261 Clearing all filters...
hook.js:608 ❌ Failed to apply filters to existing dashboard: TypeError: embeddedDashboard.setFilters is not a function
    at applyFiltersToExistingDashboard (quicksight-visual-sdk.tsx:262:35)
overrideMethod @ hook.js:608
quicksight-visual-sdk.tsx:271 Trying URL-based filter approach - will re-initialize with filtered embed URL...
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Failed to apply runtime theme: SET_THEME_OVERRIDE timed out
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
 [ECharts] DEPRECATED: 'normal' hierarchy in itemStyle has been removed since 4.0. All style properties are configured in itemStyle directly now.
Io @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
 [ECharts] DEPRECATED: textStyle hierarchy in axisLabel has been removed since 4.0. All textStyle properties are configured in axisLabel directly now.
Io @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
quicksight-visual-sdk.tsx:56 Initializing QuickSight with Runtime Theming...
quicksight-runtime-theming.ts:128 Generated theme configuration for yellow: Object
quicksight-visual-sdk.tsx:66 Applying theme options: Object
quicksight-runtime-filtering.ts:184 Parsed filters from URL: Array(0)
quicksight-visual-sdk.tsx:72 Applying runtime filters: Array(0)
quicksight-visual-sdk.tsx:73 Filter JSON for debugging: []
quicksight-visual-sdk.tsx:105 Embedding with theme options: Object
quicksight-visual-sdk.tsx:113 QuickSight embedded with runtime theming and filtering successfully
home.tsx:181 Dashboard ready for diagnostics: e14
quicksight-visual-sdk.tsx:160 Runtime theme and filter configuration applied successfully
quicksight-runtime-theming.ts:128 Generated theme configuration for yellow: Object
quicksight-visual-sdk.tsx:180 Applying runtime theme override for yellow scheme
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
quicksight-visual-sdk.tsx:251 Applying filters to existing dashboard: Array(0)
quicksight-visual-sdk.tsx:261 Clearing all filters...
hook.js:608 ❌ Failed to apply filters to existing dashboard: TypeError: embeddedDashboard.setFilters is not a function
    at applyFiltersToExistingDashboard (quicksight-visual-sdk.tsx:262:35)
overrideMethod @ hook.js:608
quicksight-visual-sdk.tsx:271 Trying URL-based filter approach - will re-initialize with filtered embed URL...
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Failed to apply runtime theme: SET_THEME_OVERRIDE timed out
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
 [ECharts] DEPRECATED: 'normal' hierarchy in itemStyle has been removed since 4.0. All style properties are configured in itemStyle directly now.
Io @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
 [ECharts] DEPRECATED: textStyle hierarchy in axisLabel has been removed since 4.0. All textStyle properties are configured in axisLabel directly now.
Io @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
quicksight-visual-sdk.tsx:56 Initializing QuickSight with Runtime Theming...
quicksight-runtime-theming.ts:128 Generated theme configuration for yellow: Object
quicksight-visual-sdk.tsx:66 Applying theme options: Object
quicksight-runtime-filtering.ts:184 Parsed filters from URL: Array(0)
quicksight-visual-sdk.tsx:72 Applying runtime filters: Array(0)
quicksight-visual-sdk.tsx:73 Filter JSON for debugging: []
quicksight-visual-sdk.tsx:105 Embedding with theme options: Object
quicksight-visual-sdk.tsx:113 QuickSight embedded with runtime theming and filtering successfully
home.tsx:181 Dashboard ready for diagnostics: e14
quicksight-visual-sdk.tsx:160 Runtime theme and filter configuration applied successfully
quicksight-runtime-theming.ts:128 Generated theme configuration for yellow: Object
quicksight-visual-sdk.tsx:180 Applying runtime theme override for yellow scheme
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
quicksight-visual-sdk.tsx:251 Applying filters to existing dashboard: Array(0)
quicksight-visual-sdk.tsx:261 Clearing all filters...
hook.js:608 ❌ Failed to apply filters to existing dashboard: TypeError: embeddedDashboard.setFilters is not a function
    at applyFiltersToExistingDashboard (quicksight-visual-sdk.tsx:262:35)
overrideMethod @ hook.js:608
quicksight-visual-sdk.tsx:271 Trying URL-based filter approach - will re-initialize with filtered embed URL...
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Failed to apply runtime theme: SET_THEME_OVERRIDE timed out
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
580.bundle.0eaeb8ea1ce2d735.7.js:2 [ECharts] DEPRECATED: 'normal' hierarchy in itemStyle has been removed since 4.0. All style properties are configured in itemStyle directly now.
Io @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
580.bundle.0eaeb8ea1ce2d735.7.js:2 [ECharts] DEPRECATED: textStyle hierarchy in axisLabel has been removed since 4.0. All textStyle properties are configured in axisLabel directly now.
Io @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
 [ECharts] Can't get DOM width or height. Please check dom.clientWidth and dom.clientHeight. They should not be 0.For example, you may need to call this in the callback of window.onload.
Io @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
To @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
Am @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
R @ 528.bundle.e98fed477fe80595.7.js:2
(anonymous) @ 112.bundle.63e5c04ec6d607f1.7.js:2
Promise.then
_rerender @ 112.bundle.63e5c04ec6d607f1.7.js:2
m @ platform-vendors.bun…16f0b934b51.58.js:2
g @ platform-vendors.bun…16f0b934b51.58.js:2
_ @ platform-vendors.bun…16f0b934b51.58.js:2
componentDidUpdate @ 112.bundle.63e5c04ec6d607f1.7.js:2
Yl @ platform-vendors.bun…16f0b934b51.58.js:2
Wl @ platform-vendors.bun…16f0b934b51.58.js:2
Zl @ platform-vendors.bun…16f0b934b51.58.js:2
(anonymous) @ platform-vendors.bun…16f0b934b51.58.js:2
qu @ platform-vendors.bun…16f0b934b51.58.js:2
Mu @ platform-vendors.bun…16f0b934b51.58.js:2
ti @ platform-vendors.bun…16f0b934b51.58.js:2
wu @ platform-vendors.bun…16f0b934b51.58.js:2
enqueueSetState @ platform-vendors.bun…16f0b934b51.58.js:2
v.setState @ platform-vendors.bun…16f0b934b51.58.js:2
_setResizeState @ 8769.bundle.1d0963b30e5f3879.58.js:2
s @ 4340.bundle.ce5b70743c5e7bb5.58.js:2
r @ 4340.bundle.ce5b70743c5e7bb5.58.js:2
m @ platform-vendors.bun…16f0b934b51.58.js:2
_ @ platform-vendors.bun…16f0b934b51.58.js:2
quicksight-visual-sdk.tsx:56 Initializing QuickSight with Runtime Theming...
quicksight-runtime-theming.ts:128 Generated theme configuration for yellow: {DataColorPalette: {…}}
quicksight-visual-sdk.tsx:66 Applying theme options: {themeOverride: {…}, preloadThemes: Array(3)}
quicksight-runtime-filtering.ts:184 Parsed filters from URL: []
quicksight-visual-sdk.tsx:72 Applying runtime filters: []
quicksight-visual-sdk.tsx:73 Filter JSON for debugging: []
quicksight-visual-sdk.tsx:105 Embedding with theme options: {themeOverride: {…}, preloadThemes: Array(3)}
 [ECharts] Can't get DOM width or height. Please check dom.clientWidth and dom.clientHeight. They should not be 0.For example, you may need to call this in the callback of window.onload.
Io @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
To @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
Am @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
R @ 528.bundle.e98fed477fe80595.7.js:2
(anonymous) @ 112.bundle.63e5c04ec6d607f1.7.js:2
Promise.then
_rerender @ 112.bundle.63e5c04ec6d607f1.7.js:2
m @ platform-vendors.bun…16f0b934b51.58.js:2
g @ platform-vendors.bun…16f0b934b51.58.js:2
_ @ platform-vendors.bun…16f0b934b51.58.js:2
componentDidUpdate @ 112.bundle.63e5c04ec6d607f1.7.js:2
Yl @ platform-vendors.bun…16f0b934b51.58.js:2
Wl @ platform-vendors.bun…16f0b934b51.58.js:2
Zl @ platform-vendors.bun…16f0b934b51.58.js:2
(anonymous) @ platform-vendors.bun…16f0b934b51.58.js:2
qu @ platform-vendors.bun…16f0b934b51.58.js:2
Mu @ platform-vendors.bun…16f0b934b51.58.js:2
ti @ platform-vendors.bun…16f0b934b51.58.js:2
wu @ platform-vendors.bun…16f0b934b51.58.js:2
enqueueSetState @ platform-vendors.bun…16f0b934b51.58.js:2
v.setState @ platform-vendors.bun…16f0b934b51.58.js:2
_setResizeState @ 8769.bundle.1d0963b30e5f3879.58.js:2
s @ 4340.bundle.ce5b70743c5e7bb5.58.js:2
r @ 4340.bundle.ce5b70743c5e7bb5.58.js:2
m @ platform-vendors.bun…16f0b934b51.58.js:2
g @ platform-vendors.bun…16f0b934b51.58.js:2
_ @ platform-vendors.bun…16f0b934b51.58.js:2
 [ECharts] Can't get DOM width or height. Please check dom.clientWidth and dom.clientHeight. They should not be 0.For example, you may need to call this in the callback of window.onload.
Io @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
To @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
Am @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
R @ 528.bundle.e98fed477fe80595.7.js:2
(anonymous) @ 112.bundle.63e5c04ec6d607f1.7.js:2
Promise.then
_rerender @ 112.bundle.63e5c04ec6d607f1.7.js:2
m @ platform-vendors.bun…16f0b934b51.58.js:2
g @ platform-vendors.bun…16f0b934b51.58.js:2
_ @ platform-vendors.bun…16f0b934b51.58.js:2
componentDidUpdate @ 112.bundle.63e5c04ec6d607f1.7.js:2
Yl @ platform-vendors.bun…16f0b934b51.58.js:2
Wl @ platform-vendors.bun…16f0b934b51.58.js:2
Zl @ platform-vendors.bun…16f0b934b51.58.js:2
(anonymous) @ platform-vendors.bun…16f0b934b51.58.js:2
qu @ platform-vendors.bun…16f0b934b51.58.js:2
Mu @ platform-vendors.bun…16f0b934b51.58.js:2
ti @ platform-vendors.bun…16f0b934b51.58.js:2
wu @ platform-vendors.bun…16f0b934b51.58.js:2
enqueueSetState @ platform-vendors.bun…16f0b934b51.58.js:2
v.setState @ platform-vendors.bun…16f0b934b51.58.js:2
_setResizeState @ 8769.bundle.1d0963b30e5f3879.58.js:2
s @ 4340.bundle.ce5b70743c5e7bb5.58.js:2
r @ 4340.bundle.ce5b70743c5e7bb5.58.js:2
m @ platform-vendors.bun…16f0b934b51.58.js:2
g @ platform-vendors.bun…16f0b934b51.58.js:2
_ @ platform-vendors.bun…16f0b934b51.58.js:2
 [ECharts] Can't get DOM width or height. Please check dom.clientWidth and dom.clientHeight. They should not be 0.For example, you may need to call this in the callback of window.onload.
Io @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
To @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
Am @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
R @ 528.bundle.e98fed477fe80595.7.js:2
(anonymous) @ 112.bundle.63e5c04ec6d607f1.7.js:2
Promise.then
_rerender @ 112.bundle.63e5c04ec6d607f1.7.js:2
m @ platform-vendors.bun…16f0b934b51.58.js:2
g @ platform-vendors.bun…16f0b934b51.58.js:2
_ @ platform-vendors.bun…16f0b934b51.58.js:2
componentDidUpdate @ 112.bundle.63e5c04ec6d607f1.7.js:2
Yl @ platform-vendors.bun…16f0b934b51.58.js:2
Wl @ platform-vendors.bun…16f0b934b51.58.js:2
Zl @ platform-vendors.bun…16f0b934b51.58.js:2
(anonymous) @ platform-vendors.bun…16f0b934b51.58.js:2
qu @ platform-vendors.bun…16f0b934b51.58.js:2
Mu @ platform-vendors.bun…16f0b934b51.58.js:2
ti @ platform-vendors.bun…16f0b934b51.58.js:2
wu @ platform-vendors.bun…16f0b934b51.58.js:2
enqueueSetState @ platform-vendors.bun…16f0b934b51.58.js:2
v.setState @ platform-vendors.bun…16f0b934b51.58.js:2
_setResizeState @ 8769.bundle.1d0963b30e5f3879.58.js:2
s @ 4340.bundle.ce5b70743c5e7bb5.58.js:2
r @ 4340.bundle.ce5b70743c5e7bb5.58.js:2
m @ platform-vendors.bun…16f0b934b51.58.js:2
g @ platform-vendors.bun…16f0b934b51.58.js:2
_ @ platform-vendors.bun…16f0b934b51.58.js:2
 [ECharts] Can't get DOM width or height. Please check dom.clientWidth and dom.clientHeight. They should not be 0.For example, you may need to call this in the callback of window.onload.
Io @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
To @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
Am @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
R @ 528.bundle.e98fed477fe80595.7.js:2
(anonymous) @ 112.bundle.63e5c04ec6d607f1.7.js:2
Promise.then
_rerender @ 112.bundle.63e5c04ec6d607f1.7.js:2
m @ platform-vendors.bun…16f0b934b51.58.js:2
g @ platform-vendors.bun…16f0b934b51.58.js:2
_ @ platform-vendors.bun…16f0b934b51.58.js:2
componentDidUpdate @ 112.bundle.63e5c04ec6d607f1.7.js:2
Yl @ platform-vendors.bun…16f0b934b51.58.js:2
Wl @ platform-vendors.bun…16f0b934b51.58.js:2
Zl @ platform-vendors.bun…16f0b934b51.58.js:2
(anonymous) @ platform-vendors.bun…16f0b934b51.58.js:2
qu @ platform-vendors.bun…16f0b934b51.58.js:2
Mu @ platform-vendors.bun…16f0b934b51.58.js:2
ti @ platform-vendors.bun…16f0b934b51.58.js:2
wu @ platform-vendors.bun…16f0b934b51.58.js:2
enqueueSetState @ platform-vendors.bun…16f0b934b51.58.js:2
v.setState @ platform-vendors.bun…16f0b934b51.58.js:2
_setResizeState @ 8769.bundle.1d0963b30e5f3879.58.js:2
s @ 4340.bundle.ce5b70743c5e7bb5.58.js:2
r @ 4340.bundle.ce5b70743c5e7bb5.58.js:2
m @ platform-vendors.bun…16f0b934b51.58.js:2
g @ platform-vendors.bun…16f0b934b51.58.js:2
_ @ platform-vendors.bun…16f0b934b51.58.js:2
 [ECharts] Can't get DOM width or height. Please check dom.clientWidth and dom.clientHeight. They should not be 0.For example, you may need to call this in the callback of window.onload.
Io @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
To @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
Am @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
R @ 528.bundle.e98fed477fe80595.7.js:2
(anonymous) @ 112.bundle.63e5c04ec6d607f1.7.js:2
Promise.then
_rerender @ 112.bundle.63e5c04ec6d607f1.7.js:2
m @ platform-vendors.bun…16f0b934b51.58.js:2
g @ platform-vendors.bun…16f0b934b51.58.js:2
_ @ platform-vendors.bun…16f0b934b51.58.js:2
componentDidUpdate @ 112.bundle.63e5c04ec6d607f1.7.js:2
Yl @ platform-vendors.bun…16f0b934b51.58.js:2
Wl @ platform-vendors.bun…16f0b934b51.58.js:2
Zl @ platform-vendors.bun…16f0b934b51.58.js:2
(anonymous) @ platform-vendors.bun…16f0b934b51.58.js:2
qu @ platform-vendors.bun…16f0b934b51.58.js:2
Mu @ platform-vendors.bun…16f0b934b51.58.js:2
ti @ platform-vendors.bun…16f0b934b51.58.js:2
wu @ platform-vendors.bun…16f0b934b51.58.js:2
enqueueSetState @ platform-vendors.bun…16f0b934b51.58.js:2
v.setState @ platform-vendors.bun…16f0b934b51.58.js:2
_setResizeState @ 8769.bundle.1d0963b30e5f3879.58.js:2
s @ 4340.bundle.ce5b70743c5e7bb5.58.js:2
r @ 4340.bundle.ce5b70743c5e7bb5.58.js:2
m @ platform-vendors.bun…16f0b934b51.58.js:2
g @ platform-vendors.bun…16f0b934b51.58.js:2
_ @ platform-vendors.bun…16f0b934b51.58.js:2
quicksight-visual-sdk.tsx:113 QuickSight embedded with runtime theming and filtering successfully
home.tsx:181 Dashboard ready for diagnostics: e14 {send: ƒ, addEventListener: ƒ, setLogProvider: ƒ, getInternalExperienceInfo: ƒ, transformContentOptions: ƒ, …}
quicksight-visual-sdk.tsx:160 Runtime theme and filter configuration applied successfully
quicksight-runtime-theming.ts:128 Generated theme configuration for yellow: {DataColorPalette: {…}}
quicksight-visual-sdk.tsx:180 Applying runtime theme override for yellow scheme
580.bundle.0eaeb8ea1ce2d735.7.js:2 [ECharts] Can't get DOM width or height. Please check dom.clientWidth and dom.clientHeight. They should not be 0.For example, you may need to call this in the callback of window.onload.
Io @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
To @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
Am @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
R @ 528.bundle.e98fed477fe80595.7.js:2
(anonymous) @ 112.bundle.63e5c04ec6d607f1.7.js:2
Promise.then
_rerender @ 112.bundle.63e5c04ec6d607f1.7.js:2
m @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
y @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
E @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
quicksight-visual-sdk.tsx:193 Failed to apply runtime theme: SET_THEME_OVERRIDE timed out
overrideMethod @ hook.js:608
applyRuntimeTheme @ quicksight-visual-sdk.tsx:193
quicksight-visual-sdk.tsx:251 Applying filters to existing dashboard: []
quicksight-visual-sdk.tsx:261 Clearing all filters...
quicksight-visual-sdk.tsx:267 ❌ Failed to apply filters to existing dashboard: TypeError: embeddedDashboard.setFilters is not a function
    at applyFiltersToExistingDashboard (quicksight-visual-sdk.tsx:262:35)
overrideMethod @ hook.js:608
applyFiltersToExistingDashboard @ quicksight-visual-sdk.tsx:267
setTimeout
(anonymous) @ quicksight-visual-sdk.tsx:280
commitHookEffectListMount @ chunk-WERSD76P.js?v=8d0075ee:16915
commitPassiveMountOnFiber @ chunk-WERSD76P.js?v=8d0075ee:18156
commitPassiveMountEffects_complete @ chunk-WERSD76P.js?v=8d0075ee:18129
commitPassiveMountEffects_begin @ chunk-WERSD76P.js?v=8d0075ee:18119
commitPassiveMountEffects @ chunk-WERSD76P.js?v=8d0075ee:18109
flushPassiveEffectsImpl @ chunk-WERSD76P.js?v=8d0075ee:19490
flushPassiveEffects @ chunk-WERSD76P.js?v=8d0075ee:19447
(anonymous) @ chunk-WERSD76P.js?v=8d0075ee:19328
workLoop @ chunk-WERSD76P.js?v=8d0075ee:197
flushWork @ chunk-WERSD76P.js?v=8d0075ee:176
performWorkUntilDeadline @ chunk-WERSD76P.js?v=8d0075ee:384
quicksight-visual-sdk.tsx:271 Trying URL-based filter approach - will re-initialize with filtered embed URL...
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
quicksight-visual-sdk.tsx:56 Initializing QuickSight with Runtime Theming...
quicksight-runtime-theming.ts:128 Generated theme configuration for yellow: {DataColorPalette: {…}}
quicksight-visual-sdk.tsx:66 Applying theme options: {themeOverride: {…}, preloadThemes: Array(3)}
quicksight-runtime-filtering.ts:184 Parsed filters from URL: []
quicksight-visual-sdk.tsx:72 Applying runtime filters: []
quicksight-visual-sdk.tsx:73 Filter JSON for debugging: []
quicksight-visual-sdk.tsx:105 Embedding with theme options: {themeOverride: {…}, preloadThemes: Array(3)}
quicksight-visual-sdk.tsx:113 QuickSight embedded with runtime theming and filtering successfully
home.tsx:181 Dashboard ready for diagnostics: e14 {send: ƒ, addEventListener: ƒ, setLogProvider: ƒ, getInternalExperienceInfo: ƒ, transformContentOptions: ƒ, …}
quicksight-visual-sdk.tsx:160 Runtime theme and filter configuration applied successfully
quicksight-runtime-theming.ts:128 Generated theme configuration for yellow: {DataColorPalette: {…}}
quicksight-visual-sdk.tsx:180 Applying runtime theme override for yellow scheme
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
quicksight-visual-sdk.tsx:193 Failed to apply runtime theme: SET_THEME_OVERRIDE timed out
overrideMethod @ hook.js:608
applyRuntimeTheme @ quicksight-visual-sdk.tsx:193
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
quicksight-visual-sdk.tsx:251 Applying filters to existing dashboard: []
quicksight-visual-sdk.tsx:261 Clearing all filters...
quicksight-visual-sdk.tsx:267 ❌ Failed to apply filters to existing dashboard: TypeError: embeddedDashboard.setFilters is not a function
    at applyFiltersToExistingDashboard (quicksight-visual-sdk.tsx:262:35)
overrideMethod @ hook.js:608
applyFiltersToExistingDashboard @ quicksight-visual-sdk.tsx:267
setTimeout
(anonymous) @ quicksight-visual-sdk.tsx:280
commitHookEffectListMount @ chunk-WERSD76P.js?v=8d0075ee:16915
commitPassiveMountOnFiber @ chunk-WERSD76P.js?v=8d0075ee:18156
commitPassiveMountEffects_complete @ chunk-WERSD76P.js?v=8d0075ee:18129
commitPassiveMountEffects_begin @ chunk-WERSD76P.js?v=8d0075ee:18119
commitPassiveMountEffects @ chunk-WERSD76P.js?v=8d0075ee:18109
flushPassiveEffectsImpl @ chunk-WERSD76P.js?v=8d0075ee:19490
flushPassiveEffects @ chunk-WERSD76P.js?v=8d0075ee:19447
(anonymous) @ chunk-WERSD76P.js?v=8d0075ee:19328
workLoop @ chunk-WERSD76P.js?v=8d0075ee:197
flushWork @ chunk-WERSD76P.js?v=8d0075ee:176
performWorkUntilDeadline @ chunk-WERSD76P.js?v=8d0075ee:384
quicksight-visual-sdk.tsx:271 Trying URL-based filter approach - will re-initialize with filtered embed URL...
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
580.bundle.0eaeb8ea1ce2d735.7.js:2 [ECharts] DEPRECATED: 'normal' hierarchy in itemStyle has been removed since 4.0. All style properties are configured in itemStyle directly now.
Io @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
Do @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
Wd @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
Ud @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
(anonymous) @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
(anonymous) @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
B @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
qd @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
of @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
(anonymous) @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
B @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
p @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
(anonymous) @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
t.setOption @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
e.setOption @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
e.setOption @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
V @ 528.bundle.e98fed477fe80595.7.js:2
R @ 528.bundle.e98fed477fe80595.7.js:2
(anonymous) @ 112.bundle.63e5c04ec6d607f1.7.js:2
Promise.then
_rerender @ 112.bundle.63e5c04ec6d607f1.7.js:2
m @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
g @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
_ @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
componentDidMount @ 112.bundle.63e5c04ec6d607f1.7.js:2
Yl @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
Wl @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
Zl @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
(anonymous) @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
qu @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
Mu @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
ti @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
wu @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
enqueueSetState @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
v.setState @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
_rerenderFromProps @ 551.bundle.0caca870d182528d.58.js:2
(anonymous) @ 1488.bundle.0547de72319fc47e.58.js:2
y @ 926.bundle.c12abacf2f3f5495.7.js:2
v @ 926.bundle.c12abacf2f3f5495.7.js:2
p @ 926.bundle.c12abacf2f3f5495.7.js:2
u.trigger @ 926.bundle.c12abacf2f3f5495.7.js:2
trigger @ 112.bundle.63e5c04ec6d607f1.7.js:2
redraw @ 112.bundle.63e5c04ec6d607f1.7.js:2
R @ 8379.bundle.269039cf70f0f648.58.js:2
_handleDataFinished @ 8379.bundle.269039cf70f0f648.58.js:2
_handleAction @ platform-libs.bundle.be09cc208c8d82cc.58.js:2
i.$Dispatcher_invokeCallback @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
i.dispatch @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
dispatch @ platform-libs.bundle.be09cc208c8d82cc.58.js:2
ee @ 1816.bundle.841f1471cf59a19b.58.js:2
I @ 8379.bundle.269039cf70f0f648.58.js:2
d.context @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
x.<computed> @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
(anonymous) @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
m @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
h @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
l @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
u @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
e.put @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
N.e.put @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
(anonymous) @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
dispatch @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
(anonymous) @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
(anonymous) @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
p @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
E @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
h @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
x.<computed> @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
(anonymous) @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
m @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
h @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
l @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
u @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
put @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
(anonymous) @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
(anonymous) @ 4662.bundle.ed4e97988b8da3da.58.js:2
m @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
h @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
u @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
s.trigger @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
forwardDone @ 6145.bundle.d39e5e2e15c87297.58.js:2
_handleMessageResponse @ 6145.bundle.d39e5e2e15c87297.58.js:2
(anonymous) @ queryworkerbootstrap.1e4df2e59f467925caa6.6.js:2
Promise.then
(anonymous) @ queryworkerbootstrap.1e4df2e59f467925caa6.6.js:2
invoke @ queryworkerbootstrap.1e4df2e59f467925caa6.6.js:2
_invokeCallbacks @ queryworkerbootstrap.1e4df2e59f467925caa6.6.js:2
WorkerListenerClient._worker.onmessage @ queryworkerbootstrap.1e4df2e59f467925caa6.6.js:2
580.bundle.0eaeb8ea1ce2d735.7.js:2 [ECharts] DEPRECATED: textStyle hierarchy in axisLabel has been removed since 4.0. All textStyle properties are configured in axisLabel directly now.
Io @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
Do @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
Yd @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
(anonymous) @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
B @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
(anonymous) @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
B @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
qd @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
of @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
(anonymous) @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
B @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
p @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
(anonymous) @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
t.setOption @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
e.setOption @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
e.setOption @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
V @ 528.bundle.e98fed477fe80595.7.js:2
R @ 528.bundle.e98fed477fe80595.7.js:2
(anonymous) @ 112.bundle.63e5c04ec6d607f1.7.js:2
Promise.then
_rerender @ 112.bundle.63e5c04ec6d607f1.7.js:2
m @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
g @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
_ @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
componentDidMount @ 112.bundle.63e5c04ec6d607f1.7.js:2
Yl @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
Wl @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
Zl @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
(anonymous) @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
qu @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
Mu @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
ti @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
wu @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
enqueueSetState @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
v.setState @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
_rerenderFromProps @ 551.bundle.0caca870d182528d.58.js:2
(anonymous) @ 1488.bundle.0547de72319fc47e.58.js:2
y @ 926.bundle.c12abacf2f3f5495.7.js:2
v @ 926.bundle.c12abacf2f3f5495.7.js:2
p @ 926.bundle.c12abacf2f3f5495.7.js:2
u.trigger @ 926.bundle.c12abacf2f3f5495.7.js:2
trigger @ 112.bundle.63e5c04ec6d607f1.7.js:2
redraw @ 112.bundle.63e5c04ec6d607f1.7.js:2
R @ 8379.bundle.269039cf70f0f648.58.js:2
_handleDataFinished @ 8379.bundle.269039cf70f0f648.58.js:2
_handleAction @ platform-libs.bundle.be09cc208c8d82cc.58.js:2
i.$Dispatcher_invokeCallback @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
i.dispatch @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
dispatch @ platform-libs.bundle.be09cc208c8d82cc.58.js:2
ee @ 1816.bundle.841f1471cf59a19b.58.js:2
I @ 8379.bundle.269039cf70f0f648.58.js:2
d.context @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
x.<computed> @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
(anonymous) @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
m @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
h @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
l @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
u @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
e.put @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
N.e.put @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
(anonymous) @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
dispatch @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
(anonymous) @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
(anonymous) @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
p @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
E @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
h @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
x.<computed> @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
(anonymous) @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
m @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
h @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
l @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
u @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
put @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
(anonymous) @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
(anonymous) @ 4662.bundle.ed4e97988b8da3da.58.js:2
m @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
h @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
u @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
s.trigger @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
forwardDone @ 6145.bundle.d39e5e2e15c87297.58.js:2
_handleMessageResponse @ 6145.bundle.d39e5e2e15c87297.58.js:2
(anonymous) @ queryworkerbootstrap.1e4df2e59f467925caa6.6.js:2
Promise.then
(anonymous) @ queryworkerbootstrap.1e4df2e59f467925caa6.6.js:2
invoke @ queryworkerbootstrap.1e4df2e59f467925caa6.6.js:2
_invokeCallbacks @ queryworkerbootstrap.1e4df2e59f467925caa6.6.js:2
WorkerListenerClient._worker.onmessage @ queryworkerbootstrap.1e4df2e59f467925caa6.6.js:2
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
580.bundle.0eaeb8ea1ce2d735.7.js:2 [ECharts] Can't get DOM width or height. Please check dom.clientWidth and dom.clientHeight. They should not be 0.For example, you may need to call this in the callback of window.onload.
Io @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
To @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
Am @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
R @ 528.bundle.e98fed477fe80595.7.js:2
(anonymous) @ 112.bundle.63e5c04ec6d607f1.7.js:2
Promise.then
_rerender @ 112.bundle.63e5c04ec6d607f1.7.js:2
m @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
g @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
_ @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
componentDidMount @ 112.bundle.63e5c04ec6d607f1.7.js:2
Yl @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
Wl @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
Zl @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
(anonymous) @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
qu @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
Mu @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
ti @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
wu @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
enqueueSetState @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
v.setState @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
_rerenderFromProps @ 551.bundle.0caca870d182528d.58.js:2
(anonymous) @ 1488.bundle.0547de72319fc47e.58.js:2
y @ 926.bundle.c12abacf2f3f5495.7.js:2
v @ 926.bundle.c12abacf2f3f5495.7.js:2
p @ 926.bundle.c12abacf2f3f5495.7.js:2
u.trigger @ 926.bundle.c12abacf2f3f5495.7.js:2
trigger @ 112.bundle.63e5c04ec6d607f1.7.js:2
redraw @ 112.bundle.63e5c04ec6d607f1.7.js:2
R @ 8379.bundle.269039cf70f0f648.58.js:2
_handleDataFinished @ 8379.bundle.269039cf70f0f648.58.js:2
_handleAction @ platform-libs.bundle.be09cc208c8d82cc.58.js:2
i.$Dispatcher_invokeCallback @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
i.dispatch @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
dispatch @ platform-libs.bundle.be09cc208c8d82cc.58.js:2
ee @ 1816.bundle.841f1471cf59a19b.58.js:2
I @ 8379.bundle.269039cf70f0f648.58.js:2
d.context @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
x.<computed> @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
(anonymous) @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
m @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
h @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
l @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
u @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
e.put @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
N.e.put @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
(anonymous) @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
dispatch @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
(anonymous) @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
(anonymous) @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
p @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
E @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
h @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
x.<computed> @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
(anonymous) @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
m @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
h @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
l @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
u @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
put @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
(anonymous) @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
(anonymous) @ 4662.bundle.ed4e97988b8da3da.58.js:2
m @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
h @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
u @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
s.trigger @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
forwardDone @ 6145.bundle.d39e5e2e15c87297.58.js:2
_handleMessageResponse @ 6145.bundle.d39e5e2e15c87297.58.js:2
(anonymous) @ queryworkerbootstrap.1e4df2e59f467925caa6.6.js:2
Promise.then
(anonymous) @ queryworkerbootstrap.1e4df2e59f467925caa6.6.js:2
invoke @ queryworkerbootstrap.1e4df2e59f467925caa6.6.js:2
_invokeCallbacks @ queryworkerbootstrap.1e4df2e59f467925caa6.6.js:2
WorkerListenerClient._worker.onmessage @ queryworkerbootstrap.1e4df2e59f467925caa6.6.js:2
580.bundle.0eaeb8ea1ce2d735.7.js:2 [ECharts] DEPRECATED: 'normal' hierarchy in itemStyle has been removed since 4.0. All style properties are configured in itemStyle directly now.
Io @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
Do @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
Wd @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
Ud @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
(anonymous) @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
(anonymous) @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
B @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
qd @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
of @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
(anonymous) @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
B @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
p @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
(anonymous) @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
t.setOption @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
e.setOption @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
e.setOption @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
V @ 528.bundle.e98fed477fe80595.7.js:2
R @ 528.bundle.e98fed477fe80595.7.js:2
(anonymous) @ 112.bundle.63e5c04ec6d607f1.7.js:2
Promise.then
_rerender @ 112.bundle.63e5c04ec6d607f1.7.js:2
m @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
g @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
_ @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
componentDidMount @ 112.bundle.63e5c04ec6d607f1.7.js:2
Yl @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
Wl @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
Zl @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
(anonymous) @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
qu @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
Mu @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
ti @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
wu @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
enqueueSetState @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
v.setState @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
_rerenderFromProps @ 551.bundle.0caca870d182528d.58.js:2
(anonymous) @ 1488.bundle.0547de72319fc47e.58.js:2
y @ 926.bundle.c12abacf2f3f5495.7.js:2
v @ 926.bundle.c12abacf2f3f5495.7.js:2
p @ 926.bundle.c12abacf2f3f5495.7.js:2
u.trigger @ 926.bundle.c12abacf2f3f5495.7.js:2
trigger @ 112.bundle.63e5c04ec6d607f1.7.js:2
redraw @ 112.bundle.63e5c04ec6d607f1.7.js:2
R @ 8379.bundle.269039cf70f0f648.58.js:2
_handleDataFinished @ 8379.bundle.269039cf70f0f648.58.js:2
_handleAction @ platform-libs.bundle.be09cc208c8d82cc.58.js:2
i.$Dispatcher_invokeCallback @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
i.dispatch @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
dispatch @ platform-libs.bundle.be09cc208c8d82cc.58.js:2
ee @ 1816.bundle.841f1471cf59a19b.58.js:2
I @ 8379.bundle.269039cf70f0f648.58.js:2
d.context @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
x.<computed> @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
(anonymous) @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
m @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
h @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
l @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
u @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
e.put @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
N.e.put @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
(anonymous) @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
dispatch @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
(anonymous) @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
(anonymous) @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
p @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
E @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
h @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
x.<computed> @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
(anonymous) @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
m @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
h @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
l @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
u @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
put @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
(anonymous) @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
(anonymous) @ 4662.bundle.ed4e97988b8da3da.58.js:2
m @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
h @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
u @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
s.trigger @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
forwardDone @ 6145.bundle.d39e5e2e15c87297.58.js:2
_handleMessageResponse @ 6145.bundle.d39e5e2e15c87297.58.js:2
(anonymous) @ queryworkerbootstrap.1e4df2e59f467925caa6.6.js:2
Promise.then
(anonymous) @ queryworkerbootstrap.1e4df2e59f467925caa6.6.js:2
invoke @ queryworkerbootstrap.1e4df2e59f467925caa6.6.js:2
_invokeCallbacks @ queryworkerbootstrap.1e4df2e59f467925caa6.6.js:2
WorkerListenerClient._worker.onmessage @ queryworkerbootstrap.1e4df2e59f467925caa6.6.js:2
580.bundle.0eaeb8ea1ce2d735.7.js:2 [ECharts] DEPRECATED: textStyle hierarchy in axisLabel has been removed since 4.0. All textStyle properties are configured in axisLabel directly now.
Io @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
Do @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
Yd @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
(anonymous) @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
B @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
(anonymous) @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
B @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
qd @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
of @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
(anonymous) @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
B @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
p @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
(anonymous) @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
t.setOption @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
e.setOption @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
e.setOption @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
V @ 528.bundle.e98fed477fe80595.7.js:2
R @ 528.bundle.e98fed477fe80595.7.js:2
(anonymous) @ 112.bundle.63e5c04ec6d607f1.7.js:2
Promise.then
_rerender @ 112.bundle.63e5c04ec6d607f1.7.js:2
m @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
g @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
_ @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
componentDidMount @ 112.bundle.63e5c04ec6d607f1.7.js:2
Yl @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
Wl @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
Zl @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
(anonymous) @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
qu @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
Mu @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
ti @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
wu @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
enqueueSetState @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
v.setState @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
_rerenderFromProps @ 551.bundle.0caca870d182528d.58.js:2
(anonymous) @ 1488.bundle.0547de72319fc47e.58.js:2
y @ 926.bundle.c12abacf2f3f5495.7.js:2
v @ 926.bundle.c12abacf2f3f5495.7.js:2
p @ 926.bundle.c12abacf2f3f5495.7.js:2
u.trigger @ 926.bundle.c12abacf2f3f5495.7.js:2
trigger @ 112.bundle.63e5c04ec6d607f1.7.js:2
redraw @ 112.bundle.63e5c04ec6d607f1.7.js:2
R @ 8379.bundle.269039cf70f0f648.58.js:2
_handleDataFinished @ 8379.bundle.269039cf70f0f648.58.js:2
_handleAction @ platform-libs.bundle.be09cc208c8d82cc.58.js:2
i.$Dispatcher_invokeCallback @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
i.dispatch @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
dispatch @ platform-libs.bundle.be09cc208c8d82cc.58.js:2
ee @ 1816.bundle.841f1471cf59a19b.58.js:2
I @ 8379.bundle.269039cf70f0f648.58.js:2
d.context @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
x.<computed> @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
(anonymous) @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
m @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
h @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
l @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
u @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
e.put @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
N.e.put @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
(anonymous) @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
dispatch @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
(anonymous) @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
(anonymous) @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
p @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
E @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
h @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
x.<computed> @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
(anonymous) @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
m @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
h @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
l @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
u @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
put @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
(anonymous) @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
(anonymous) @ 4662.bundle.ed4e97988b8da3da.58.js:2
m @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
h @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
u @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
s.trigger @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
forwardDone @ 6145.bundle.d39e5e2e15c87297.58.js:2
_handleMessageResponse @ 6145.bundle.d39e5e2e15c87297.58.js:2
(anonymous) @ queryworkerbootstrap.1e4df2e59f467925caa6.6.js:2
Promise.then
(anonymous) @ queryworkerbootstrap.1e4df2e59f467925caa6.6.js:2
invoke @ queryworkerbootstrap.1e4df2e59f467925caa6.6.js:2
_invokeCallbacks @ queryworkerbootstrap.1e4df2e59f467925caa6.6.js:2
WorkerListenerClient._worker.onmessage @ queryworkerbootstrap.1e4df2e59f467925caa6.6.js:2
580.bundle.0eaeb8ea1ce2d735.7.js:2 [ECharts] Can't get DOM width or height. Please check dom.clientWidth and dom.clientHeight. They should not be 0.For example, you may need to call this in the callback of window.onload.
Io @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
To @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
Am @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
R @ 528.bundle.e98fed477fe80595.7.js:2
(anonymous) @ 112.bundle.63e5c04ec6d607f1.7.js:2
Promise.then
_rerender @ 112.bundle.63e5c04ec6d607f1.7.js:2
m @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
y @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
E @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
setTimeout
Pi @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
E @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
setTimeout
Pi @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
g @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
_ @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
componentDidMount @ 112.bundle.63e5c04ec6d607f1.7.js:2
Yl @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
Wl @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
Zl @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
(anonymous) @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
qu @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
Mu @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
ti @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
wu @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
enqueueSetState @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
v.setState @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
_rerenderFromProps @ 551.bundle.0caca870d182528d.58.js:2
(anonymous) @ 1488.bundle.0547de72319fc47e.58.js:2
y @ 926.bundle.c12abacf2f3f5495.7.js:2
v @ 926.bundle.c12abacf2f3f5495.7.js:2
p @ 926.bundle.c12abacf2f3f5495.7.js:2
u.trigger @ 926.bundle.c12abacf2f3f5495.7.js:2
trigger @ 112.bundle.63e5c04ec6d607f1.7.js:2
redraw @ 112.bundle.63e5c04ec6d607f1.7.js:2
R @ 8379.bundle.269039cf70f0f648.58.js:2
_handleDataFinished @ 8379.bundle.269039cf70f0f648.58.js:2
_handleAction @ platform-libs.bundle.be09cc208c8d82cc.58.js:2
i.$Dispatcher_invokeCallback @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
i.dispatch @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
dispatch @ platform-libs.bundle.be09cc208c8d82cc.58.js:2
ee @ 1816.bundle.841f1471cf59a19b.58.js:2
I @ 8379.bundle.269039cf70f0f648.58.js:2
d.context @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
x.<computed> @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
(anonymous) @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
m @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
h @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
l @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
u @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
e.put @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
N.e.put @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
(anonymous) @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
dispatch @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
(anonymous) @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
(anonymous) @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
p @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
E @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
h @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
x.<computed> @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
(anonymous) @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
m @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
h @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
l @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
u @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
put @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
(anonymous) @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
(anonymous) @ 4662.bundle.ed4e97988b8da3da.58.js:2
m @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
h @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
u @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
s.trigger @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
forwardDone @ 6145.bundle.d39e5e2e15c87297.58.js:2
_handleMessageResponse @ 6145.bundle.d39e5e2e15c87297.58.js:2
(anonymous) @ queryworkerbootstrap.1e4df2e59f467925caa6.6.js:2
Promise.then
(anonymous) @ queryworkerbootstrap.1e4df2e59f467925caa6.6.js:2
invoke @ queryworkerbootstrap.1e4df2e59f467925caa6.6.js:2
_invokeCallbacks @ queryworkerbootstrap.1e4df2e59f467925caa6.6.js:2
WorkerListenerClient._worker.onmessage @ queryworkerbootstrap.1e4df2e59f467925caa6.6.js:2
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
quicksight-visual-sdk.tsx:56 Initializing QuickSight with Runtime Theming...
quicksight-runtime-theming.ts:128 Generated theme configuration for yellow: {DataColorPalette: {…}}
quicksight-visual-sdk.tsx:66 Applying theme options: {themeOverride: {…}, preloadThemes: Array(3)}
quicksight-runtime-filtering.ts:184 Parsed filters from URL: []
quicksight-visual-sdk.tsx:72 Applying runtime filters: []
quicksight-visual-sdk.tsx:73 Filter JSON for debugging: []
quicksight-visual-sdk.tsx:105 Embedding with theme options: {themeOverride: {…}, preloadThemes: Array(3)}
quicksight-visual-sdk.tsx:113 QuickSight embedded with runtime theming and filtering successfully
home.tsx:181 Dashboard ready for diagnostics: e14 {send: ƒ, addEventListener: ƒ, setLogProvider: ƒ, getInternalExperienceInfo: ƒ, transformContentOptions: ƒ, …}
quicksight-visual-sdk.tsx:160 Runtime theme and filter configuration applied successfully
quicksight-runtime-theming.ts:128 Generated theme configuration for yellow: {DataColorPalette: {…}}
quicksight-visual-sdk.tsx:180 Applying runtime theme override for yellow scheme
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
quicksight-visual-sdk.tsx:251 Applying filters to existing dashboard: []
quicksight-visual-sdk.tsx:261 Clearing all filters...
quicksight-visual-sdk.tsx:267 ❌ Failed to apply filters to existing dashboard: TypeError: embeddedDashboard.setFilters is not a function
    at applyFiltersToExistingDashboard (quicksight-visual-sdk.tsx:262:35)
overrideMethod @ hook.js:608
applyFiltersToExistingDashboard @ quicksight-visual-sdk.tsx:267
setTimeout
(anonymous) @ quicksight-visual-sdk.tsx:280
commitHookEffectListMount @ chunk-WERSD76P.js?v=8d0075ee:16915
commitPassiveMountOnFiber @ chunk-WERSD76P.js?v=8d0075ee:18156
commitPassiveMountEffects_complete @ chunk-WERSD76P.js?v=8d0075ee:18129
commitPassiveMountEffects_begin @ chunk-WERSD76P.js?v=8d0075ee:18119
commitPassiveMountEffects @ chunk-WERSD76P.js?v=8d0075ee:18109
flushPassiveEffectsImpl @ chunk-WERSD76P.js?v=8d0075ee:19490
flushPassiveEffects @ chunk-WERSD76P.js?v=8d0075ee:19447
(anonymous) @ chunk-WERSD76P.js?v=8d0075ee:19328
workLoop @ chunk-WERSD76P.js?v=8d0075ee:197
flushWork @ chunk-WERSD76P.js?v=8d0075ee:176
performWorkUntilDeadline @ chunk-WERSD76P.js?v=8d0075ee:384
quicksight-visual-sdk.tsx:271 Trying URL-based filter approach - will re-initialize with filtered embed URL...
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
quicksight-visual-sdk.tsx:193 Failed to apply runtime theme: SET_THEME_OVERRIDE timed out
overrideMethod @ hook.js:608
applyRuntimeTheme @ quicksight-visual-sdk.tsx:193
await in applyRuntimeTheme
(anonymous) @ quicksight-visual-sdk.tsx:242
commitHookEffectListMount @ chunk-WERSD76P.js?v=8d0075ee:16915
commitPassiveMountOnFiber @ chunk-WERSD76P.js?v=8d0075ee:18156
commitPassiveMountEffects_complete @ chunk-WERSD76P.js?v=8d0075ee:18129
commitPassiveMountEffects_begin @ chunk-WERSD76P.js?v=8d0075ee:18119
commitPassiveMountEffects @ chunk-WERSD76P.js?v=8d0075ee:18109
flushPassiveEffectsImpl @ chunk-WERSD76P.js?v=8d0075ee:19490
flushPassiveEffects @ chunk-WERSD76P.js?v=8d0075ee:19447
(anonymous) @ chunk-WERSD76P.js?v=8d0075ee:19328
workLoop @ chunk-WERSD76P.js?v=8d0075ee:197
flushWork @ chunk-WERSD76P.js?v=8d0075ee:176
performWorkUntilDeadline @ chunk-WERSD76P.js?v=8d0075ee:384
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
580.bundle.0eaeb8ea1ce2d735.7.js:2 [ECharts] Can't get DOM width or height. Please check dom.clientWidth and dom.clientHeight. They should not be 0.For example, you may need to call this in the callback of window.onload.
Io @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
To @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
Am @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
R @ 528.bundle.e98fed477fe80595.7.js:2
(anonymous) @ 112.bundle.63e5c04ec6d607f1.7.js:2
Promise.then
_rerender @ 112.bundle.63e5c04ec6d607f1.7.js:2
m @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
g @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
_ @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
componentDidMount @ 112.bundle.63e5c04ec6d607f1.7.js:2
Yl @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
Wl @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
Zl @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
(anonymous) @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
qu @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
Mu @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
ti @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
wu @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
enqueueSetState @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
v.setState @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
_rerenderFromProps @ 551.bundle.0caca870d182528d.58.js:2
(anonymous) @ 1488.bundle.0547de72319fc47e.58.js:2
y @ 926.bundle.c12abacf2f3f5495.7.js:2
v @ 926.bundle.c12abacf2f3f5495.7.js:2
p @ 926.bundle.c12abacf2f3f5495.7.js:2
u.trigger @ 926.bundle.c12abacf2f3f5495.7.js:2
trigger @ 112.bundle.63e5c04ec6d607f1.7.js:2
redraw @ 112.bundle.63e5c04ec6d607f1.7.js:2
R @ 8379.bundle.269039cf70f0f648.58.js:2
_handleDataFinished @ 8379.bundle.269039cf70f0f648.58.js:2
_handleAction @ platform-libs.bundle.be09cc208c8d82cc.58.js:2
i.$Dispatcher_invokeCallback @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
i.dispatch @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
dispatch @ platform-libs.bundle.be09cc208c8d82cc.58.js:2
ee @ 1816.bundle.841f1471cf59a19b.58.js:2
I @ 8379.bundle.269039cf70f0f648.58.js:2
d.context @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
x.<computed> @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
(anonymous) @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
m @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
h @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
l @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
u @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
e.put @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
N.e.put @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
(anonymous) @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
dispatch @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
(anonymous) @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
(anonymous) @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
p @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
E @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
h @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
x.<computed> @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
(anonymous) @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
m @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
h @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
l @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
u @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
put @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
(anonymous) @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
(anonymous) @ 4662.bundle.ed4e97988b8da3da.58.js:2
m @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
h @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
u @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
s.trigger @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
forwardDone @ 6145.bundle.d39e5e2e15c87297.58.js:2
_handleMessageResponse @ 6145.bundle.d39e5e2e15c87297.58.js:2
(anonymous) @ queryworkerbootstrap.1e4df2e59f467925caa6.6.js:2
Promise.then
(anonymous) @ queryworkerbootstrap.1e4df2e59f467925caa6.6.js:2
invoke @ queryworkerbootstrap.1e4df2e59f467925caa6.6.js:2
_invokeCallbacks @ queryworkerbootstrap.1e4df2e59f467925caa6.6.js:2
WorkerListenerClient._worker.onmessage @ queryworkerbootstrap.1e4df2e59f467925caa6.6.js:2
580.bundle.0eaeb8ea1ce2d735.7.js:2 [ECharts] DEPRECATED: 'normal' hierarchy in itemStyle has been removed since 4.0. All style properties are configured in itemStyle directly now.
Io @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
Do @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
Wd @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
Ud @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
(anonymous) @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
(anonymous) @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
B @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
qd @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
of @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
(anonymous) @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
B @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
p @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
(anonymous) @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
t.setOption @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
e.setOption @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
e.setOption @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
V @ 528.bundle.e98fed477fe80595.7.js:2
R @ 528.bundle.e98fed477fe80595.7.js:2
(anonymous) @ 112.bundle.63e5c04ec6d607f1.7.js:2
Promise.then
_rerender @ 112.bundle.63e5c04ec6d607f1.7.js:2
m @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
g @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
_ @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
componentDidMount @ 112.bundle.63e5c04ec6d607f1.7.js:2
Yl @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
Wl @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
Zl @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
(anonymous) @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
qu @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
Mu @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
ti @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
wu @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
enqueueSetState @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
v.setState @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
_rerenderFromProps @ 551.bundle.0caca870d182528d.58.js:2
(anonymous) @ 1488.bundle.0547de72319fc47e.58.js:2
y @ 926.bundle.c12abacf2f3f5495.7.js:2
v @ 926.bundle.c12abacf2f3f5495.7.js:2
p @ 926.bundle.c12abacf2f3f5495.7.js:2
u.trigger @ 926.bundle.c12abacf2f3f5495.7.js:2
trigger @ 112.bundle.63e5c04ec6d607f1.7.js:2
redraw @ 112.bundle.63e5c04ec6d607f1.7.js:2
R @ 8379.bundle.269039cf70f0f648.58.js:2
_handleDataFinished @ 8379.bundle.269039cf70f0f648.58.js:2
_handleAction @ platform-libs.bundle.be09cc208c8d82cc.58.js:2
i.$Dispatcher_invokeCallback @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
i.dispatch @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
dispatch @ platform-libs.bundle.be09cc208c8d82cc.58.js:2
ee @ 1816.bundle.841f1471cf59a19b.58.js:2
I @ 8379.bundle.269039cf70f0f648.58.js:2
d.context @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
x.<computed> @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
(anonymous) @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
m @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
h @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
l @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
u @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
e.put @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
N.e.put @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
(anonymous) @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
dispatch @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
(anonymous) @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
(anonymous) @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
p @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
E @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
h @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
x.<computed> @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
(anonymous) @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
m @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
h @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
l @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
u @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
put @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
(anonymous) @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
(anonymous) @ 4662.bundle.ed4e97988b8da3da.58.js:2
m @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
h @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
u @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
s.trigger @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
forwardDone @ 6145.bundle.d39e5e2e15c87297.58.js:2
_handleMessageResponse @ 6145.bundle.d39e5e2e15c87297.58.js:2
(anonymous) @ queryworkerbootstrap.1e4df2e59f467925caa6.6.js:2
Promise.then
(anonymous) @ queryworkerbootstrap.1e4df2e59f467925caa6.6.js:2
invoke @ queryworkerbootstrap.1e4df2e59f467925caa6.6.js:2
_invokeCallbacks @ queryworkerbootstrap.1e4df2e59f467925caa6.6.js:2
WorkerListenerClient._worker.onmessage @ queryworkerbootstrap.1e4df2e59f467925caa6.6.js:2
580.bundle.0eaeb8ea1ce2d735.7.js:2 [ECharts] DEPRECATED: textStyle hierarchy in axisLabel has been removed since 4.0. All textStyle properties are configured in axisLabel directly now.
Io @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
Do @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
Yd @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
(anonymous) @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
B @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
(anonymous) @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
B @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
qd @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
of @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
(anonymous) @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
B @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
p @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
(anonymous) @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
t.setOption @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
e.setOption @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
e.setOption @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
V @ 528.bundle.e98fed477fe80595.7.js:2
R @ 528.bundle.e98fed477fe80595.7.js:2
(anonymous) @ 112.bundle.63e5c04ec6d607f1.7.js:2
Promise.then
_rerender @ 112.bundle.63e5c04ec6d607f1.7.js:2
m @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
g @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
_ @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
componentDidMount @ 112.bundle.63e5c04ec6d607f1.7.js:2
Yl @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
Wl @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
Zl @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
(anonymous) @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
qu @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
Mu @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
ti @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
wu @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
enqueueSetState @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
v.setState @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
_rerenderFromProps @ 551.bundle.0caca870d182528d.58.js:2
(anonymous) @ 1488.bundle.0547de72319fc47e.58.js:2
y @ 926.bundle.c12abacf2f3f5495.7.js:2
v @ 926.bundle.c12abacf2f3f5495.7.js:2
p @ 926.bundle.c12abacf2f3f5495.7.js:2
u.trigger @ 926.bundle.c12abacf2f3f5495.7.js:2
trigger @ 112.bundle.63e5c04ec6d607f1.7.js:2
redraw @ 112.bundle.63e5c04ec6d607f1.7.js:2
R @ 8379.bundle.269039cf70f0f648.58.js:2
_handleDataFinished @ 8379.bundle.269039cf70f0f648.58.js:2
_handleAction @ platform-libs.bundle.be09cc208c8d82cc.58.js:2
i.$Dispatcher_invokeCallback @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
i.dispatch @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
dispatch @ platform-libs.bundle.be09cc208c8d82cc.58.js:2
ee @ 1816.bundle.841f1471cf59a19b.58.js:2
I @ 8379.bundle.269039cf70f0f648.58.js:2
d.context @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
x.<computed> @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
(anonymous) @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
m @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
h @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
l @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
u @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
e.put @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
N.e.put @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
(anonymous) @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
dispatch @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
(anonymous) @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
(anonymous) @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
p @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
E @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
h @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
x.<computed> @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
(anonymous) @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
m @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
h @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
l @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
u @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
put @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
(anonymous) @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
(anonymous) @ 4662.bundle.ed4e97988b8da3da.58.js:2
m @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
h @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
u @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
s.trigger @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
forwardDone @ 6145.bundle.d39e5e2e15c87297.58.js:2
_handleMessageResponse @ 6145.bundle.d39e5e2e15c87297.58.js:2
(anonymous) @ queryworkerbootstrap.1e4df2e59f467925caa6.6.js:2
Promise.then
(anonymous) @ queryworkerbootstrap.1e4df2e59f467925caa6.6.js:2
invoke @ queryworkerbootstrap.1e4df2e59f467925caa6.6.js:2
_invokeCallbacks @ queryworkerbootstrap.1e4df2e59f467925caa6.6.js:2
WorkerListenerClient._worker.onmessage @ queryworkerbootstrap.1e4df2e59f467925caa6.6.js:2
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
quicksight-visual-sdk.tsx:56 Initializing QuickSight with Runtime Theming...
quicksight-runtime-theming.ts:128 Generated theme configuration for yellow: {DataColorPalette: {…}}
quicksight-visual-sdk.tsx:66 Applying theme options: {themeOverride: {…}, preloadThemes: Array(3)}
quicksight-runtime-filtering.ts:184 Parsed filters from URL: []
quicksight-visual-sdk.tsx:72 Applying runtime filters: []
quicksight-visual-sdk.tsx:73 Filter JSON for debugging: []
quicksight-visual-sdk.tsx:105 Embedding with theme options: {themeOverride: {…}, preloadThemes: Array(3)}
quicksight-visual-sdk.tsx:113 QuickSight embedded with runtime theming and filtering successfully
home.tsx:181 Dashboard ready for diagnostics: e14 {send: ƒ, addEventListener: ƒ, setLogProvider: ƒ, getInternalExperienceInfo: ƒ, transformContentOptions: ƒ, …}
quicksight-visual-sdk.tsx:160 Runtime theme and filter configuration applied successfully
quicksight-runtime-theming.ts:128 Generated theme configuration for yellow: {DataColorPalette: {…}}
quicksight-visual-sdk.tsx:180 Applying runtime theme override for yellow scheme
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
quicksight-visual-sdk.tsx:251 Applying filters to existing dashboard: []
quicksight-visual-sdk.tsx:261 Clearing all filters...
quicksight-visual-sdk.tsx:267 ❌ Failed to apply filters to existing dashboard: TypeError: embeddedDashboard.setFilters is not a function
    at applyFiltersToExistingDashboard (quicksight-visual-sdk.tsx:262:35)
overrideMethod @ hook.js:608
applyFiltersToExistingDashboard @ quicksight-visual-sdk.tsx:267
setTimeout
(anonymous) @ quicksight-visual-sdk.tsx:280
commitHookEffectListMount @ chunk-WERSD76P.js?v=8d0075ee:16915
commitPassiveMountOnFiber @ chunk-WERSD76P.js?v=8d0075ee:18156
commitPassiveMountEffects_complete @ chunk-WERSD76P.js?v=8d0075ee:18129
commitPassiveMountEffects_begin @ chunk-WERSD76P.js?v=8d0075ee:18119
commitPassiveMountEffects @ chunk-WERSD76P.js?v=8d0075ee:18109
flushPassiveEffectsImpl @ chunk-WERSD76P.js?v=8d0075ee:19490
flushPassiveEffects @ chunk-WERSD76P.js?v=8d0075ee:19447
(anonymous) @ chunk-WERSD76P.js?v=8d0075ee:19328
workLoop @ chunk-WERSD76P.js?v=8d0075ee:197
flushWork @ chunk-WERSD76P.js?v=8d0075ee:176
performWorkUntilDeadline @ chunk-WERSD76P.js?v=8d0075ee:384
quicksight-visual-sdk.tsx:271 Trying URL-based filter approach - will re-initialize with filtered embed URL...
quicksight-visual-sdk.tsx:193 Failed to apply runtime theme: SET_THEME_OVERRIDE timed out
overrideMethod @ hook.js:608
applyRuntimeTheme @ quicksight-visual-sdk.tsx:193
await in applyRuntimeTheme
(anonymous) @ quicksight-visual-sdk.tsx:242
commitHookEffectListMount @ chunk-WERSD76P.js?v=8d0075ee:16915
commitPassiveMountOnFiber @ chunk-WERSD76P.js?v=8d0075ee:18156
commitPassiveMountEffects_complete @ chunk-WERSD76P.js?v=8d0075ee:18129
commitPassiveMountEffects_begin @ chunk-WERSD76P.js?v=8d0075ee:18119
commitPassiveMountEffects @ chunk-WERSD76P.js?v=8d0075ee:18109
flushPassiveEffectsImpl @ chunk-WERSD76P.js?v=8d0075ee:19490
flushPassiveEffects @ chunk-WERSD76P.js?v=8d0075ee:19447
(anonymous) @ chunk-WERSD76P.js?v=8d0075ee:19328
workLoop @ chunk-WERSD76P.js?v=8d0075ee:197
flushWork @ chunk-WERSD76P.js?v=8d0075ee:176
performWorkUntilDeadline @ chunk-WERSD76P.js?v=8d0075ee:384
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
