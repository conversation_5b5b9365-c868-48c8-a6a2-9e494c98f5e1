 Starting nGrams value 0
 Initializing QuickSight with Runtime Theming...
 Generated theme configuration for yellow: Object
 Applying theme options: Object
 Parsed filters from URL: Array(0)
 Applying runtime filters: Array(0)
 Embedding with theme options: Object
 QuickSight embedded with runtime theming and filtering successfully
 Runtime theme and filter configuration applied successfully
 Generated theme configuration for yellow: Object
 Applying runtime theme override for yellow scheme
Failed to execute 'postMessage' on 'DOMWindow': The target origin provided ('<URL>') does not match the recipient window's origin ('<URL>').
Failed to execute 'postMessage' on 'DOMWindow': The target origin provided ('<URL>') does not match the recipient window's origin ('<URL>').
Failed to execute 'postMessage' on 'DOMWindow': The target origin provided ('<URL>') does not match the recipient window's origin ('<URL>').
Failed to execute 'postMessage' on 'DOMWindow': The target origin provided ('<URL>') does not match the recipient window's origin ('<URL>').
Failed to execute 'postMessage' on 'DOMWindow': The target origin provided ('<URL>') does not match the recipient window's origin ('<URL>').
Failed to execute 'postMessage' on 'DOMWindow': The target origin provided ('<URL>') does not match the recipient window's origin ('<URL>').
Failed to execute 'postMessage' on 'DOMWindow': The target origin provided ('<URL>') does not match the recipient window's origin ('<URL>').
Failed to execute 'postMessage' on 'DOMWindow': The target origin provided ('<URL>') does not match the recipient window's origin ('<URL>').
 [ECharts] DEPRECATED: 'normal' hierarchy in itemStyle has been removed since 4.0. All style properties are configured in itemStyle directly now.
Io @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
Do @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
Wd @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
Ud @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
(anonymous) @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
(anonymous) @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
B @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
qd @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
of @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
(anonymous) @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
B @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
p @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
(anonymous) @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
t.setOption @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
e.setOption @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
e.setOption @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
V @ 528.bundle.e98fed477fe80595.7.js:2
R @ 528.bundle.e98fed477fe80595.7.js:2
(anonymous) @ 112.bundle.63e5c04ec6d607f1.7.js:2
 [ECharts] DEPRECATED: textStyle hierarchy in axisLabel has been removed since 4.0. All textStyle properties are configured in axisLabel directly now.
Io @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
Do @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
Yd @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
(anonymous) @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
B @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
(anonymous) @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
B @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
qd @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
of @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
(anonymous) @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
B @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
p @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
(anonymous) @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
t.setOption @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
e.setOption @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
e.setOption @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
V @ 528.bundle.e98fed477fe80595.7.js:2
R @ 528.bundle.e98fed477fe80595.7.js:2
(anonymous) @ 112.bundle.63e5c04ec6d607f1.7.js:2
 Failed to apply runtime theme: SET_THEME_OVERRIDE timed out
overrideMethod @ installHook.js:1
applyRuntimeTheme @ quicksight-visual-sdk.tsx:132
 Generated QuickSight-compatible filters: Array(1)
 Filters updated: Array(1)
 Filters changed, re-initializing QuickSight visual...
 Initializing QuickSight with Runtime Theming...
 Generated theme configuration for yellow: Object
 Applying theme options: Object
 Parsed filters from URL: Array(0)
 Applying runtime filters: Array(1)
 Embedding with theme options: Object
 QuickSight embedded with runtime theming and filtering successfully
 Runtime filters applied successfully
 Runtime theme and filter configuration applied successfully
 Generated theme configuration for yellow: Object
 Applying runtime theme override for yellow scheme
 Message with unrecognized event target received
overrideMethod @ installHook.js:1
(anonymous) @ amazon-quicksight-em….js?v=8d0075ee:1796
 Message with unrecognized event target received
overrideMethod @ installHook.js:1
(anonymous) @ amazon-quicksight-em….js?v=8d0075ee:1796
 Message with unrecognized event target received
overrideMethod @ installHook.js:1
(anonymous) @ amazon-quicksight-em….js?v=8d0075ee:1796
 Message with unrecognized event target received
overrideMethod @ installHook.js:1
(anonymous) @ amazon-quicksight-em….js?v=8d0075ee:1796
 Message with unrecognized event target received
overrideMethod @ installHook.js:1
(anonymous) @ amazon-quicksight-em….js?v=8d0075ee:1796
 Message with unrecognized event target received
overrideMethod @ installHook.js:1
(anonymous) @ amazon-quicksight-em….js?v=8d0075ee:1796
 [ECharts] DEPRECATED: 'normal' hierarchy in itemStyle has been removed since 4.0. All style properties are configured in itemStyle directly now.
Io @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
Do @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
Wd @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
Ud @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
(anonymous) @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
(anonymous) @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
B @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
qd @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
of @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
(anonymous) @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
B @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
p @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
(anonymous) @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
t.setOption @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
e.setOption @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
e.setOption @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
V @ 528.bundle.e98fed477fe80595.7.js:2
R @ 528.bundle.e98fed477fe80595.7.js:2
(anonymous) @ 112.bundle.63e5c04ec6d607f1.7.js:2
 [ECharts] DEPRECATED: textStyle hierarchy in axisLabel has been removed since 4.0. All textStyle properties are configured in axisLabel directly now.
Io @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
Do @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
Yd @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
(anonymous) @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
B @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
(anonymous) @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
B @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
qd @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
of @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
(anonymous) @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
B @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
p @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
(anonymous) @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
t.setOption @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
e.setOption @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
e.setOption @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
V @ 528.bundle.e98fed477fe80595.7.js:2
R @ 528.bundle.e98fed477fe80595.7.js:2
(anonymous) @ 112.bundle.63e5c04ec6d607f1.7.js:2
 Failed to apply runtime theme: SET_THEME_OVERRIDE timed out
overrideMethod @ installHook.js:1
applyRuntimeTheme @ quicksight-visual-sdk.tsx:132
 Generated QuickSight-compatible filters: Array(1)
 Filters updated: Array(1)
 Filters changed, re-initializing QuickSight visual...
 Generated QuickSight-compatible filters: Array(1)
 Filters updated: Array(1)
 Filters changed, re-initializing QuickSight visual...
 Initializing QuickSight with Runtime Theming...
 Generated theme configuration for yellow: Object
 Applying theme options: Object
 Parsed filters from URL: Array(0)
 Applying runtime filters: Array(1)
 Embedding with theme options: Object
 QuickSight embedded with runtime theming and filtering successfully
 Runtime filters applied successfully
 Runtime theme and filter configuration applied successfully
 Generated theme configuration for yellow: Object
 Applying runtime theme override for yellow scheme
 Message with unrecognized event target received
overrideMethod @ installHook.js:1
 Message with unrecognized event target received
overrideMethod @ installHook.js:1
 Initializing QuickSight with Runtime Theming...
 Generated theme configuration for yellow: Object
 Applying theme options: Object
 Parsed filters from URL: Array(0)
 Applying runtime filters: Array(1)
 Embedding with theme options: Object
 QuickSight embedded with runtime theming and filtering successfully
 Runtime filters applied successfully
 Runtime theme and filter configuration applied successfully
 Generated theme configuration for yellow: Object
quicksight-visual-sdk.tsx:146 Applying runtime theme override for yellow scheme
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
 [ECharts] DEPRECATED: 'normal' hierarchy in itemStyle has been removed since 4.0. All style properties are configured in itemStyle directly now.
Io @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
 [ECharts] DEPRECATED: textStyle hierarchy in axisLabel has been removed since 4.0. All textStyle properties are configured in axisLabel directly now.
Io @ d758cqe2bs24d.cloudf…b8ea1ce2d735.7.js:2
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
 [ECharts] DEPRECATED: 'normal' hierarchy in itemStyle has been removed since 4.0. All style properties are configured in itemStyle directly now.
Io @ d758cqe2bs24d.cloudf…b8ea1ce2d735.7.js:2
 [ECharts] DEPRECATED: textStyle hierarchy in axisLabel has been removed since 4.0. All textStyle properties are configured in axisLabel directly now.
Io @ d758cqe2bs24d.cloudf…b8ea1ce2d735.7.js:2
real-data-filter-controls.tsx:108 Generated QuickSight-compatible filters: Array(1)
home.tsx:53 Filters updated: Array(1)
quicksight-visual-sdk.tsx:215 Filters changed, re-initializing QuickSight visual...
real-data-filter-controls.tsx:108 Generated QuickSight-compatible filters: Array(1)
home.tsx:53 Filters updated: Array(1)
quicksight-visual-sdk.tsx:215 Filters changed, re-initializing QuickSight visual...
quicksight-visual-sdk.tsx:55 Initializing QuickSight with Runtime Theming...
quicksight-runtime-theming.ts:128 Generated theme configuration for yellow: Object
quicksight-visual-sdk.tsx:65 Applying theme options: Object
quicksight-runtime-filtering.ts:184 Parsed filters from URL: Array(0)
quicksight-visual-sdk.tsx:71 Applying runtime filters: Array(1)
quicksight-visual-sdk.tsx:106 Embedding with theme options: Object
quicksight-visual-sdk.tsx:114 QuickSight embedded with runtime theming and filtering successfully
quicksight-visual-sdk.tsx:122 Runtime filters applied successfully
quicksight-visual-sdk.tsx:126 Runtime theme and filter configuration applied successfully
quicksight-runtime-theming.ts:128 Generated theme configuration for yellow: Object
quicksight-visual-sdk.tsx:146 Applying runtime theme override for yellow scheme
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
quicksight-visual-sdk.tsx:55 Initializing QuickSight with Runtime Theming...
quicksight-runtime-theming.ts:128 Generated theme configuration for yellow: Object
quicksight-visual-sdk.tsx:65 Applying theme options: Object
quicksight-runtime-filtering.ts:184 Parsed filters from URL: Array(0)
quicksight-visual-sdk.tsx:71 Applying runtime filters: Array(1)
quicksight-visual-sdk.tsx:106 Embedding with theme options: Object
quicksight-visual-sdk.tsx:114 QuickSight embedded with runtime theming and filtering successfully
quicksight-visual-sdk.tsx:122 Runtime filters applied successfully
quicksight-visual-sdk.tsx:126 Runtime theme and filter configuration applied successfully
quicksight-runtime-theming.ts:128 Generated theme configuration for yellow: Object
quicksight-visual-sdk.tsx:146 Applying runtime theme override for yellow scheme
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
 [ECharts] DEPRECATED: 'normal' hierarchy in itemStyle has been removed since 4.0. All style properties are configured in itemStyle directly now.
Io @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
 [ECharts] DEPRECATED: textStyle hierarchy in axisLabel has been removed since 4.0. All textStyle properties are configured in axisLabel directly now.
Io @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Failed to apply runtime theme: SET_THEME_OVERRIDE timed out
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
 [ECharts] DEPRECATED: 'normal' hierarchy in itemStyle has been removed since 4.0. All style properties are configured in itemStyle directly now.
Io @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
 [ECharts] DEPRECATED: textStyle hierarchy in axisLabel has been removed since 4.0. All textStyle properties are configured in axisLabel directly now.
Io @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
hook.js:608 Failed to apply runtime theme: SET_THEME_OVERRIDE timed out
overrideMethod @ hook.js:608
real-data-filter-controls.tsx:108 Generated QuickSight-compatible filters: Array(1)
home.tsx:53 Filters updated: Array(1)
quicksight-visual-sdk.tsx:215 Filters changed, re-initializing QuickSight visual...
hook.js:608 Failed to apply runtime theme: SET_THEME_OVERRIDE timed out
overrideMethod @ hook.js:608
hook.js:608 Failed to apply runtime theme: SET_THEME_OVERRIDE timed out
overrideMethod @ hook.js:608
quicksight-visual-sdk.tsx:55 Initializing QuickSight with Runtime Theming...
quicksight-runtime-theming.ts:128 Generated theme configuration for yellow: Object
quicksight-visual-sdk.tsx:65 Applying theme options: Object
quicksight-runtime-filtering.ts:184 Parsed filters from URL: Array(0)
quicksight-visual-sdk.tsx:71 Applying runtime filters: Array(1)
quicksight-visual-sdk.tsx:106 Embedding with theme options: Object
quicksight-visual-sdk.tsx:114 QuickSight embedded with runtime theming and filtering successfully
quicksight-visual-sdk.tsx:122 Runtime filters applied successfully
quicksight-visual-sdk.tsx:126 Runtime theme and filter configuration applied successfully
quicksight-runtime-theming.ts:128 Generated theme configuration for yellow: Object
quicksight-visual-sdk.tsx:146 Applying runtime theme override for yellow scheme
real-data-filter-controls.tsx:108 Generated QuickSight-compatible filters: Array(1)
home.tsx:53 Filters updated: Array(1)
quicksight-visual-sdk.tsx:215 Filters changed, re-initializing QuickSight visual...
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
quicksight-visual-sdk.tsx:55 Initializing QuickSight with Runtime Theming...
quicksight-runtime-theming.ts:128 Generated theme configuration for yellow: Object
quicksight-visual-sdk.tsx:65 Applying theme options: Object
quicksight-runtime-filtering.ts:184 Parsed filters from URL: Array(0)
quicksight-visual-sdk.tsx:71 Applying runtime filters: Array(1)
quicksight-visual-sdk.tsx:106 Embedding with theme options: Object
quicksight-visual-sdk.tsx:114 QuickSight embedded with runtime theming and filtering successfully
quicksight-visual-sdk.tsx:122 Runtime filters applied successfully
quicksight-visual-sdk.tsx:126 Runtime theme and filter configuration applied successfully
quicksight-runtime-theming.ts:128 Generated theme configuration for yellow: Object
quicksight-visual-sdk.tsx:146 Applying runtime theme override for yellow scheme
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
 [ECharts] Can't get DOM width or height. Please check dom.clientWidth and dom.clientHeight. They should not be 0.For example, you may need to call this in the callback of window.onload.
Io @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
 [ECharts] DEPRECATED: 'normal' hierarchy in itemStyle has been removed since 4.0. All style properties are configured in itemStyle directly now.
Io @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
 [ECharts] DEPRECATED: textStyle hierarchy in axisLabel has been removed since 4.0. All textStyle properties are configured in axisLabel directly now.
Io @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
 [ECharts] DEPRECATED: 'normal' hierarchy in itemStyle has been removed since 4.0. All style properties are configured in itemStyle directly now.
Io @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
 [ECharts] DEPRECATED: textStyle hierarchy in axisLabel has been removed since 4.0. All textStyle properties are configured in axisLabel directly now.
Io @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
real-data-filter-controls.tsx:108 Generated QuickSight-compatible filters: Array(1)
home.tsx:53 Filters updated: Array(1)
quicksight-visual-sdk.tsx:215 Filters changed, re-initializing QuickSight visual...
quicksight-visual-sdk.tsx:55 Initializing QuickSight with Runtime Theming...
quicksight-runtime-theming.ts:128 Generated theme configuration for yellow: Object
quicksight-visual-sdk.tsx:65 Applying theme options: Object
quicksight-runtime-filtering.ts:184 Parsed filters from URL: Array(0)
quicksight-visual-sdk.tsx:71 Applying runtime filters: Array(1)
quicksight-visual-sdk.tsx:106 Embedding with theme options: Object
quicksight-visual-sdk.tsx:114 QuickSight embedded with runtime theming and filtering successfully
quicksight-visual-sdk.tsx:122 Runtime filters applied successfully
quicksight-visual-sdk.tsx:126 Runtime theme and filter configuration applied successfully
quicksight-runtime-theming.ts:128 Generated theme configuration for yellow: Object
quicksight-visual-sdk.tsx:146 Applying runtime theme override for yellow scheme
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Failed to apply runtime theme: SET_THEME_OVERRIDE timed out
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
580.bundle.0eaeb8ea1ce2d735.7.js:2 [ECharts] DEPRECATED: 'normal' hierarchy in itemStyle has been removed since 4.0. All style properties are configured in itemStyle directly now.
Io @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
580.bundle.0eaeb8ea1ce2d735.7.js:2 [ECharts] DEPRECATED: textStyle hierarchy in axisLabel has been removed since 4.0. All textStyle properties are configured in axisLabel directly now.
Io @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
hook.js:608 Failed to apply runtime theme: SET_THEME_OVERRIDE timed out
overrideMethod @ hook.js:608
us-east-1.quicksight.aws.amazon.com/embed/a730115ba3e2467ba75cf333e029fdda/dashboards/b991ee42-96e1-4591-b999-f5457b751d07?code=AYABeK5Mz1eEjKT0mFl3c87yyOIAAAABAAdhd3Mta21zAEthcm46YXdzOmttczp1cy1lYXN0LTE6MjU5NDgwNDYyMTMyOmtleS81NGYwMjdiYy03MDJhLTQxY2YtYmViNS0xNDViOTExNzFkYzMAuAECAQB4g1oL4hdUJZc1aKfcGo-VQb_jBEsh0RowAd9MxoJqXpEBDQaFL7Pp2nfLlgp49hIneAAAAH4wfAYJKoZIhvcNAQcGoG8wbQIBADBoBgkqhkiG9w0BBwEwHgYJYIZIAWUDBAEuMBEEDCJRRVUVVPLi7Xaz4gIBEIA76JL667pJtmgiyy2-EnbDpkNr1B8r9ybgFkE2zRqhqTlJNNUq2AF64iSO9KVuqhTb1xb5DBH7XH2Axw0CAAAAAAwAABAAAAAAAAAAAAAAAAAAPhaY0MV_x9SRfb3Nt8j_Q_____8AAAABAAAAAAAAAAAAAAABAAAAm6fKic4sLvRAf1U0QR6Ukrf0yWrVqL9Mkm325Jxt1hlfbR2YEJZ9Qhrvp8cpQSctMHcOwfrh7My7gDstUvSpFeKOHoGlW69uWL_yb53NBEheUC-ixarQoTY2ER-fGG7Z2_2uXqqbOqMKrv2mYJrQ3t-k_QkP_ZUjSlK93wmgvo67PhoyZd3mllUhA2bM8sVepKMgTaGuD-gUeWKNCw0oP8vUo3Rn0isQTcrq5w%3D%3D&identityprovider=quicksight&isauthcode=true&punyCodeEmbedOrigin=https%3A%2F%2F832ffbef-e0a6-46ce-b724-ecae2aa10510-00-63k1yesr7dy8.spock.replit.dev%2F-&sdkVersion=2.10.1&footerPaddingEnabled=true&undoRedoDisabled=true&resetDisabled=true&contextId=7ec4d94b-3b03-4ec6-af8d-05ade4225d46&discriminator=0#p.colorScheme=yellow:1 Blocked aria-hidden on an element because its descendant retained focus. The focus must not be hidden from assistive technology users. Avoid using aria-hidden on a focused element or its ancestor. Consider using the inert attribute instead, which will also prevent focus. For more details, see the aria-hidden section of the WAI-ARIA specification at https://w3c.github.io/aria/#aria-hidden.
Element with focus: <div.MuiSelect-root-1317 MuiSelect-select-1318 MuiSelect-selectMenu-1321 MuiSelect-outlined-1320 MuiInputBase-input-1347 MuiOutlinedInput-input-1334 VegaTextFieldInput MuiInputBase-inputMarginDense-1348 MuiOutlinedInput-inputMarginDense-1335#SHEET_CONTROL-d40144af-4bce-490f-aea7-55512d8d4e37-id>
Ancestor with aria-hidden: <div.layout vertical> <div class=​"layout vertical" style=​"height:​ 100%;​">​…​</div>​flex
hook.js:608 Failed to apply runtime theme: SET_THEME_OVERRIDE timed out
overrideMethod @ hook.js:608
web-client-content-script.js:2 TypeError: e.getRootNode(...).getElementById is not a function
    at web-client-content-script.js:2:405800
    at web-client-content-script.js:2:405025
    at web-client-content-script.js:2:437043
    at e (web-client-content-script.js:2:437057)
    at Array.forEach (<anonymous>)
    at web-client-content-script.js:2:437130
    at nu (web-client-content-script.js:2:139076)
    at kl (web-client-content-script.js:2:159303)
    at ul (web-client-content-script.js:2:151560)
    at Wa (web-client-content-script.js:2:92268)
ds @ web-client-content-script.js:2
web-client-content-script.js:2 TypeError: Cannot read properties of undefined (reading 'disconnect')
    at web-client-content-script.js:2:404560
    at eu (web-client-content-script.js:2:138711)
    at ru (web-client-content-script.js:2:138907)
    at kl (web-client-content-script.js:2:158554)
    at web-client-content-script.js:2:155893
    at k (web-client-content-script.js:2:197112)
    at MessagePort.L (web-client-content-script.js:2:197646)
ds @ web-client-content-script.js:2
home.tsx:53 Filters updated: Array(0)
 [ECharts] Can't get DOM width or height. Please check dom.clientWidth and dom.clientHeight. They should not be 0.For example, you may need to call this in the callback of window.onload.
Io @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
 [ECharts] Can't get DOM width or height. Please check dom.clientWidth and dom.clientHeight. They should not be 0.For example, you may need to call this in the callback of window.onload.
Io @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
To @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
Am @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
R @ 528.bundle.e98fed477fe80595.7.js:2
(anonymous) @ 112.bundle.63e5c04ec6d607f1.7.js:2
Promise.then
_rerender @ 112.bundle.63e5c04ec6d607f1.7.js:2
m @ platform-vendors.bun…16f0b934b51.58.js:2
y @ platform-vendors.bun…16f0b934b51.58.js:2
E @ platform-vendors.bun…16f0b934b51.58.js:2
