contentScript.js:3 Starting nGrams value 0
quicksight-visual-sdk.tsx:214 Filters changed, re-initializing QuickSight visual...
quicksight-visual-sdk.tsx:55 Initializing QuickSight with Runtime Theming...
quicksight-runtime-theming.ts:128 Generated theme configuration for yellow: {DataColorPalette: {…}}
quicksight-visual-sdk.tsx:65 Applying theme options: {themeOverride: {…}, preloadThemes: Array(3)}
quicksight-runtime-filtering.ts:184 Parsed filters from URL: []
quicksight-visual-sdk.tsx:71 Applying runtime filters: []
quicksight-visual-sdk.tsx:72 Filter JSON for debugging: []
quicksight-visual-sdk.tsx:104 Embedding with theme options: {themeOverride: {…}, preloadThemes: Array(3)}
quicksight-visual-sdk.tsx:112 QuickSight embedded with runtime theming and filtering successfully
quicksight-visual-sdk.tsx:124 Runtime theme and filter configuration applied successfully
quicksight-runtime-theming.ts:128 Generated theme configuration for yellow: {DataColorPalette: {…}}
quicksight-visual-sdk.tsx:144 Applying runtime theme override for yellow scheme
quicksight-visual-sdk.tsx:148 Failed to execute 'postMessage' on 'DOMWindow': The target origin provided ('https://us-east-1.quicksight.aws.amazon.com') does not match the recipient window's origin ('https://832ffbef-e0a6-46ce-b724-ecae2aa10510-00-63k1yesr7dy8.spock.replit.dev').
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:663
p2 @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:143
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:220
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:166
n @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:11
s2 @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:23
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:28
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:20
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:685
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1784
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:655
p2 @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:143
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:220
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:166
n @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:11
s2 @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:23
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:28
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:20
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:685
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:501
p2 @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:143
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:220
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:166
n @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:11
s2 @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:23
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:28
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:20
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:509
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1533
p2 @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:143
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:220
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:166
n @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:11
s2 @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:23
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:28
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:20
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1541
applyRuntimeTheme @ quicksight-visual-sdk.tsx:148
(anonymous) @ quicksight-visual-sdk.tsx:206
commitHookEffectListMount @ chunk-WERSD76P.js?v=8d0075ee:16915
commitPassiveMountOnFiber @ chunk-WERSD76P.js?v=8d0075ee:18156
commitPassiveMountEffects_complete @ chunk-WERSD76P.js?v=8d0075ee:18129
commitPassiveMountEffects_begin @ chunk-WERSD76P.js?v=8d0075ee:18119
commitPassiveMountEffects @ chunk-WERSD76P.js?v=8d0075ee:18109
flushPassiveEffectsImpl @ chunk-WERSD76P.js?v=8d0075ee:19490
flushPassiveEffects @ chunk-WERSD76P.js?v=8d0075ee:19447
(anonymous) @ chunk-WERSD76P.js?v=8d0075ee:19328
workLoop @ chunk-WERSD76P.js?v=8d0075ee:197
flushWork @ chunk-WERSD76P.js?v=8d0075ee:176
performWorkUntilDeadline @ chunk-WERSD76P.js?v=8d0075ee:384
832ffbef-e0a6-46ce-b724-ecae2aa10510-00-63k1yesr7dy8.spock.replit.dev/:1 Unchecked runtime.lastError: The message port closed before a response was received.
quicksight-visual-sdk.tsx:55 Initializing QuickSight with Runtime Theming...
quicksight-runtime-theming.ts:128 Generated theme configuration for yellow: {DataColorPalette: {…}}
quicksight-visual-sdk.tsx:65 Applying theme options: {themeOverride: {…}, preloadThemes: Array(3)}
quicksight-runtime-filtering.ts:184 Parsed filters from URL: []
quicksight-visual-sdk.tsx:71 Applying runtime filters: []
quicksight-visual-sdk.tsx:72 Filter JSON for debugging: []
quicksight-visual-sdk.tsx:104 Embedding with theme options: {themeOverride: {…}, preloadThemes: Array(3)}
quicksight-visual-sdk.tsx:112 QuickSight embedded with runtime theming and filtering successfully
quicksight-visual-sdk.tsx:124 Runtime theme and filter configuration applied successfully
quicksight-runtime-theming.ts:128 Generated theme configuration for yellow: {DataColorPalette: {…}}
quicksight-visual-sdk.tsx:144 Applying runtime theme override for yellow scheme
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
580.bundle.0eaeb8ea1ce2d735.7.js:2 [ECharts] DEPRECATED: 'normal' hierarchy in itemStyle has been removed since 4.0. All style properties are configured in itemStyle directly now.
Io @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
Do @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
Wd @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
Ud @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
(anonymous) @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
(anonymous) @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
B @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
qd @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
of @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
(anonymous) @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
B @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
p @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
(anonymous) @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
t.setOption @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
e.setOption @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
e.setOption @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
V @ 528.bundle.e98fed477fe80595.7.js:2
R @ 528.bundle.e98fed477fe80595.7.js:2
(anonymous) @ 112.bundle.63e5c04ec6d607f1.7.js:2
Promise.then
_rerender @ 112.bundle.63e5c04ec6d607f1.7.js:2
m @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
g @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
_ @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
componentDidMount @ 112.bundle.63e5c04ec6d607f1.7.js:2
Yl @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
Wl @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
Zl @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
(anonymous) @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
qu @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
Mu @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
ti @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
wu @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
enqueueSetState @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
v.setState @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
_rerenderFromProps @ 551.bundle.0caca870d182528d.58.js:2
(anonymous) @ 1488.bundle.0547de72319fc47e.58.js:2
y @ 926.bundle.c12abacf2f3f5495.7.js:2
v @ 926.bundle.c12abacf2f3f5495.7.js:2
p @ 926.bundle.c12abacf2f3f5495.7.js:2
u.trigger @ 926.bundle.c12abacf2f3f5495.7.js:2
trigger @ 112.bundle.63e5c04ec6d607f1.7.js:2
redraw @ 112.bundle.63e5c04ec6d607f1.7.js:2
R @ 8379.bundle.269039cf70f0f648.58.js:2
_handleDataFinished @ 8379.bundle.269039cf70f0f648.58.js:2
_handleAction @ platform-libs.bundle.be09cc208c8d82cc.58.js:2
i.$Dispatcher_invokeCallback @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
i.dispatch @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
dispatch @ platform-libs.bundle.be09cc208c8d82cc.58.js:2
ee @ 1816.bundle.841f1471cf59a19b.58.js:2
I @ 8379.bundle.269039cf70f0f648.58.js:2
d.context @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
x.<computed> @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
(anonymous) @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
m @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
h @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
l @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
u @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
e.put @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
N.e.put @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
(anonymous) @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
dispatch @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
(anonymous) @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
(anonymous) @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
p @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
E @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
h @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
x.<computed> @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
(anonymous) @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
m @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
h @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
l @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
u @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
put @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
(anonymous) @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
(anonymous) @ 4662.bundle.ed4e97988b8da3da.58.js:2
m @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
h @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
u @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
s.trigger @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
forwardDone @ 6145.bundle.d39e5e2e15c87297.58.js:2
_handleMessageResponse @ 6145.bundle.d39e5e2e15c87297.58.js:2
(anonymous) @ queryworkerbootstrap.1e4df2e59f467925caa6.6.js:2
Promise.then
(anonymous) @ queryworkerbootstrap.1e4df2e59f467925caa6.6.js:2
invoke @ queryworkerbootstrap.1e4df2e59f467925caa6.6.js:2
_invokeCallbacks @ queryworkerbootstrap.1e4df2e59f467925caa6.6.js:2
WorkerListenerClient._worker.onmessage @ queryworkerbootstrap.1e4df2e59f467925caa6.6.js:2
580.bundle.0eaeb8ea1ce2d735.7.js:2 [ECharts] DEPRECATED: textStyle hierarchy in axisLabel has been removed since 4.0. All textStyle properties are configured in axisLabel directly now.
Io @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
Do @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
Yd @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
(anonymous) @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
B @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
(anonymous) @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
B @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
qd @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
of @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
(anonymous) @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
B @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
p @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
(anonymous) @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
t.setOption @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
e.setOption @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
e.setOption @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
V @ 528.bundle.e98fed477fe80595.7.js:2
R @ 528.bundle.e98fed477fe80595.7.js:2
(anonymous) @ 112.bundle.63e5c04ec6d607f1.7.js:2
Promise.then
_rerender @ 112.bundle.63e5c04ec6d607f1.7.js:2
m @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
g @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
_ @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
componentDidMount @ 112.bundle.63e5c04ec6d607f1.7.js:2
Yl @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
Wl @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
Zl @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
(anonymous) @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
qu @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
Mu @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
ti @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
wu @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
enqueueSetState @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
v.setState @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
_rerenderFromProps @ 551.bundle.0caca870d182528d.58.js:2
(anonymous) @ 1488.bundle.0547de72319fc47e.58.js:2
y @ 926.bundle.c12abacf2f3f5495.7.js:2
v @ 926.bundle.c12abacf2f3f5495.7.js:2
p @ 926.bundle.c12abacf2f3f5495.7.js:2
u.trigger @ 926.bundle.c12abacf2f3f5495.7.js:2
trigger @ 112.bundle.63e5c04ec6d607f1.7.js:2
redraw @ 112.bundle.63e5c04ec6d607f1.7.js:2
R @ 8379.bundle.269039cf70f0f648.58.js:2
_handleDataFinished @ 8379.bundle.269039cf70f0f648.58.js:2
_handleAction @ platform-libs.bundle.be09cc208c8d82cc.58.js:2
i.$Dispatcher_invokeCallback @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
i.dispatch @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
dispatch @ platform-libs.bundle.be09cc208c8d82cc.58.js:2
ee @ 1816.bundle.841f1471cf59a19b.58.js:2
I @ 8379.bundle.269039cf70f0f648.58.js:2
d.context @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
x.<computed> @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
(anonymous) @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
m @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
h @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
l @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
u @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
e.put @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
N.e.put @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
(anonymous) @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
dispatch @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
(anonymous) @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
(anonymous) @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
p @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
E @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
h @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
x.<computed> @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
(anonymous) @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
m @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
h @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
l @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
u @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
put @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
(anonymous) @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
(anonymous) @ 4662.bundle.ed4e97988b8da3da.58.js:2
m @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
h @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
u @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
s.trigger @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
forwardDone @ 6145.bundle.d39e5e2e15c87297.58.js:2
_handleMessageResponse @ 6145.bundle.d39e5e2e15c87297.58.js:2
(anonymous) @ queryworkerbootstrap.1e4df2e59f467925caa6.6.js:2
Promise.then
(anonymous) @ queryworkerbootstrap.1e4df2e59f467925caa6.6.js:2
invoke @ queryworkerbootstrap.1e4df2e59f467925caa6.6.js:2
_invokeCallbacks @ queryworkerbootstrap.1e4df2e59f467925caa6.6.js:2
WorkerListenerClient._worker.onmessage @ queryworkerbootstrap.1e4df2e59f467925caa6.6.js:2
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
580.bundle.0eaeb8ea1ce2d735.7.js:2 [ECharts] DEPRECATED: 'normal' hierarchy in itemStyle has been removed since 4.0. All style properties are configured in itemStyle directly now.
Io @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
Do @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
Wd @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
Ud @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
(anonymous) @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
(anonymous) @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
B @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
qd @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
of @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
(anonymous) @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
B @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
p @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
(anonymous) @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
t.setOption @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
e.setOption @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
e.setOption @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
V @ 528.bundle.e98fed477fe80595.7.js:2
R @ 528.bundle.e98fed477fe80595.7.js:2
(anonymous) @ 112.bundle.63e5c04ec6d607f1.7.js:2
Promise.then
_rerender @ 112.bundle.63e5c04ec6d607f1.7.js:2
m @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
g @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
_ @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
componentDidMount @ 112.bundle.63e5c04ec6d607f1.7.js:2
Yl @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
Wl @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
Zl @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
(anonymous) @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
qu @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
Mu @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
ti @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
wu @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
enqueueSetState @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
v.setState @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
_rerenderFromProps @ 551.bundle.0caca870d182528d.58.js:2
(anonymous) @ 1488.bundle.0547de72319fc47e.58.js:2
y @ 926.bundle.c12abacf2f3f5495.7.js:2
v @ 926.bundle.c12abacf2f3f5495.7.js:2
p @ 926.bundle.c12abacf2f3f5495.7.js:2
u.trigger @ 926.bundle.c12abacf2f3f5495.7.js:2
trigger @ 112.bundle.63e5c04ec6d607f1.7.js:2
redraw @ 112.bundle.63e5c04ec6d607f1.7.js:2
R @ 8379.bundle.269039cf70f0f648.58.js:2
_handleDataFinished @ 8379.bundle.269039cf70f0f648.58.js:2
_handleAction @ platform-libs.bundle.be09cc208c8d82cc.58.js:2
i.$Dispatcher_invokeCallback @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
i.dispatch @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
dispatch @ platform-libs.bundle.be09cc208c8d82cc.58.js:2
ee @ 1816.bundle.841f1471cf59a19b.58.js:2
I @ 8379.bundle.269039cf70f0f648.58.js:2
d.context @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
x.<computed> @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
(anonymous) @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
m @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
h @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
l @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
u @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
e.put @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
N.e.put @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
(anonymous) @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
dispatch @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
(anonymous) @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
(anonymous) @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
p @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
E @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
h @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
x.<computed> @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
(anonymous) @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
m @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
h @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
l @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
u @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
put @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
(anonymous) @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
(anonymous) @ 4662.bundle.ed4e97988b8da3da.58.js:2
m @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
h @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
u @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
s.trigger @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
forwardDone @ 6145.bundle.d39e5e2e15c87297.58.js:2
_handleMessageResponse @ 6145.bundle.d39e5e2e15c87297.58.js:2
(anonymous) @ queryworkerbootstrap.1e4df2e59f467925caa6.6.js:2
Promise.then
(anonymous) @ queryworkerbootstrap.1e4df2e59f467925caa6.6.js:2
invoke @ queryworkerbootstrap.1e4df2e59f467925caa6.6.js:2
_invokeCallbacks @ queryworkerbootstrap.1e4df2e59f467925caa6.6.js:2
WorkerListenerClient._worker.onmessage @ queryworkerbootstrap.1e4df2e59f467925caa6.6.js:2
580.bundle.0eaeb8ea1ce2d735.7.js:2 [ECharts] DEPRECATED: textStyle hierarchy in axisLabel has been removed since 4.0. All textStyle properties are configured in axisLabel directly now.
Io @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
Do @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
Yd @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
(anonymous) @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
B @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
(anonymous) @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
B @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
qd @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
of @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
(anonymous) @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
B @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
p @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
(anonymous) @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
t.setOption @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
e.setOption @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
e.setOption @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
V @ 528.bundle.e98fed477fe80595.7.js:2
R @ 528.bundle.e98fed477fe80595.7.js:2
(anonymous) @ 112.bundle.63e5c04ec6d607f1.7.js:2
Promise.then
_rerender @ 112.bundle.63e5c04ec6d607f1.7.js:2
m @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
g @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
_ @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
componentDidMount @ 112.bundle.63e5c04ec6d607f1.7.js:2
Yl @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
Wl @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
Zl @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
(anonymous) @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
qu @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
Mu @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
ti @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
wu @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
enqueueSetState @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
v.setState @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
_rerenderFromProps @ 551.bundle.0caca870d182528d.58.js:2
(anonymous) @ 1488.bundle.0547de72319fc47e.58.js:2
y @ 926.bundle.c12abacf2f3f5495.7.js:2
v @ 926.bundle.c12abacf2f3f5495.7.js:2
p @ 926.bundle.c12abacf2f3f5495.7.js:2
u.trigger @ 926.bundle.c12abacf2f3f5495.7.js:2
trigger @ 112.bundle.63e5c04ec6d607f1.7.js:2
redraw @ 112.bundle.63e5c04ec6d607f1.7.js:2
R @ 8379.bundle.269039cf70f0f648.58.js:2
_handleDataFinished @ 8379.bundle.269039cf70f0f648.58.js:2
_handleAction @ platform-libs.bundle.be09cc208c8d82cc.58.js:2
i.$Dispatcher_invokeCallback @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
i.dispatch @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
dispatch @ platform-libs.bundle.be09cc208c8d82cc.58.js:2
ee @ 1816.bundle.841f1471cf59a19b.58.js:2
I @ 8379.bundle.269039cf70f0f648.58.js:2
d.context @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
x.<computed> @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
(anonymous) @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
m @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
h @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
l @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
u @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
e.put @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
N.e.put @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
(anonymous) @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
dispatch @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
(anonymous) @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
(anonymous) @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
p @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
E @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
h @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
x.<computed> @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
(anonymous) @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
m @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
h @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
l @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
u @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
put @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
(anonymous) @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
(anonymous) @ 4662.bundle.ed4e97988b8da3da.58.js:2
m @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
h @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
u @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
s.trigger @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
forwardDone @ 6145.bundle.d39e5e2e15c87297.58.js:2
_handleMessageResponse @ 6145.bundle.d39e5e2e15c87297.58.js:2
(anonymous) @ queryworkerbootstrap.1e4df2e59f467925caa6.6.js:2
Promise.then
(anonymous) @ queryworkerbootstrap.1e4df2e59f467925caa6.6.js:2
invoke @ queryworkerbootstrap.1e4df2e59f467925caa6.6.js:2
_invokeCallbacks @ queryworkerbootstrap.1e4df2e59f467925caa6.6.js:2
WorkerListenerClient._worker.onmessage @ queryworkerbootstrap.1e4df2e59f467925caa6.6.js:2
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
quicksight-visual-sdk.tsx:157 Failed to apply runtime theme: SET_THEME_OVERRIDE timed out
overrideMethod @ hook.js:608
applyRuntimeTheme @ quicksight-visual-sdk.tsx:157
await in applyRuntimeTheme
(anonymous) @ quicksight-visual-sdk.tsx:206
commitHookEffectListMount @ chunk-WERSD76P.js?v=8d0075ee:16915
commitPassiveMountOnFiber @ chunk-WERSD76P.js?v=8d0075ee:18156
commitPassiveMountEffects_complete @ chunk-WERSD76P.js?v=8d0075ee:18129
commitPassiveMountEffects_begin @ chunk-WERSD76P.js?v=8d0075ee:18119
commitPassiveMountEffects @ chunk-WERSD76P.js?v=8d0075ee:18109
flushPassiveEffectsImpl @ chunk-WERSD76P.js?v=8d0075ee:19490
flushPassiveEffects @ chunk-WERSD76P.js?v=8d0075ee:19447
(anonymous) @ chunk-WERSD76P.js?v=8d0075ee:19328
workLoop @ chunk-WERSD76P.js?v=8d0075ee:197
flushWork @ chunk-WERSD76P.js?v=8d0075ee:176
performWorkUntilDeadline @ chunk-WERSD76P.js?v=8d0075ee:384
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
quicksight-visual-sdk.tsx:157 Failed to apply runtime theme: SET_THEME_OVERRIDE timed out
overrideMethod @ hook.js:608
applyRuntimeTheme @ quicksight-visual-sdk.tsx:157
await in applyRuntimeTheme
(anonymous) @ quicksight-visual-sdk.tsx:206
commitHookEffectListMount @ chunk-WERSD76P.js?v=8d0075ee:16915
commitPassiveMountOnFiber @ chunk-WERSD76P.js?v=8d0075ee:18156
commitPassiveMountEffects_complete @ chunk-WERSD76P.js?v=8d0075ee:18129
commitPassiveMountEffects_begin @ chunk-WERSD76P.js?v=8d0075ee:18119
commitPassiveMountEffects @ chunk-WERSD76P.js?v=8d0075ee:18109
flushPassiveEffectsImpl @ chunk-WERSD76P.js?v=8d0075ee:19490
flushPassiveEffects @ chunk-WERSD76P.js?v=8d0075ee:19447
(anonymous) @ chunk-WERSD76P.js?v=8d0075ee:19328
workLoop @ chunk-WERSD76P.js?v=8d0075ee:197
flushWork @ chunk-WERSD76P.js?v=8d0075ee:176
performWorkUntilDeadline @ chunk-WERSD76P.js?v=8d0075ee:384
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
real-data-filter-controls.tsx:95 Generated QuickSight-compatible filters: [{…}]0: {FilterId: '19211674-bbdc-4ecf-9613-6d9e54e55487', CategoryFilter: {…}}length: 1[[Prototype]]: Array(0)
home.tsx:53 Filters updated: [{…}]
quicksight-visual-sdk.tsx:214 Filters changed, re-initializing QuickSight visual...
quicksight-visual-sdk.tsx:55 Initializing QuickSight with Runtime Theming...
quicksight-runtime-theming.ts:128 Generated theme configuration for yellow: {DataColorPalette: {…}}
quicksight-visual-sdk.tsx:65 Applying theme options: {themeOverride: {…}, preloadThemes: Array(3)}
quicksight-runtime-filtering.ts:184 Parsed filters from URL: []
quicksight-visual-sdk.tsx:71 Applying runtime filters: [{…}]
quicksight-visual-sdk.tsx:72 Filter JSON for debugging: [
  {
    "FilterId": "19211674-bbdc-4ecf-9613-6d9e54e55487",
    "CategoryFilter": {
      "Column": {
        "DataSetIdentifier": "wdata.csv",
        "ColumnName": "State"
      },
      "Configuration": {
        "FilterListConfiguration": {
          "MatchOperator": "EQUALS",
          "CategoryValues": [
            "Arkansas"
          ]
        }
      }
    }
  }
]
quicksight-visual-sdk.tsx:104 Embedding with theme options: {themeOverride: {…}, preloadThemes: Array(3)}
quicksight-visual-sdk.tsx:112 QuickSight embedded with runtime theming and filtering successfully
quicksight-visual-sdk.tsx:120 Runtime filters applied successfully
quicksight-visual-sdk.tsx:124 Runtime theme and filter configuration applied successfully
quicksight-runtime-theming.ts:128 Generated theme configuration for yellow: {DataColorPalette: {…}}
quicksight-visual-sdk.tsx:144 Applying runtime theme override for yellow scheme
quicksight-visual-sdk.tsx:148 Failed to execute 'postMessage' on 'DOMWindow': The target origin provided ('https://us-east-1.quicksight.aws.amazon.com') does not match the recipient window's origin ('https://832ffbef-e0a6-46ce-b724-ecae2aa10510-00-63k1yesr7dy8.spock.replit.dev').
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:663
p2 @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:143
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:220
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:166
n @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:11
s2 @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:23
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:28
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:20
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:685
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1784
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:655
p2 @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:143
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:220
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:166
n @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:11
s2 @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:23
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:28
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:20
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:685
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:501
p2 @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:143
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:220
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:166
n @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:11
s2 @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:23
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:28
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:20
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:509
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1533
p2 @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:143
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:220
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:166
n @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:11
s2 @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:23
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:28
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:20
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1541
applyRuntimeTheme @ quicksight-visual-sdk.tsx:148
(anonymous) @ quicksight-visual-sdk.tsx:206
commitHookEffectListMount @ chunk-WERSD76P.js?v=8d0075ee:16915
commitPassiveMountOnFiber @ chunk-WERSD76P.js?v=8d0075ee:18156
commitPassiveMountEffects_complete @ chunk-WERSD76P.js?v=8d0075ee:18129
commitPassiveMountEffects_begin @ chunk-WERSD76P.js?v=8d0075ee:18119
commitPassiveMountEffects @ chunk-WERSD76P.js?v=8d0075ee:18109
flushPassiveEffectsImpl @ chunk-WERSD76P.js?v=8d0075ee:19490
flushPassiveEffects @ chunk-WERSD76P.js?v=8d0075ee:19447
(anonymous) @ chunk-WERSD76P.js?v=8d0075ee:19328
workLoop @ chunk-WERSD76P.js?v=8d0075ee:197
flushWork @ chunk-WERSD76P.js?v=8d0075ee:176
performWorkUntilDeadline @ chunk-WERSD76P.js?v=8d0075ee:384
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
580.bundle.0eaeb8ea1ce2d735.7.js:2 [ECharts] DEPRECATED: 'normal' hierarchy in itemStyle has been removed since 4.0. All style properties are configured in itemStyle directly now.
Io @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
Do @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
Wd @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
Ud @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
(anonymous) @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
(anonymous) @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
B @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
qd @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
of @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
(anonymous) @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
B @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
p @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
(anonymous) @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
t.setOption @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
e.setOption @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
e.setOption @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
V @ 528.bundle.e98fed477fe80595.7.js:2
R @ 528.bundle.e98fed477fe80595.7.js:2
(anonymous) @ 112.bundle.63e5c04ec6d607f1.7.js:2
Promise.then
_rerender @ 112.bundle.63e5c04ec6d607f1.7.js:2
m @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
g @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
_ @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
componentDidMount @ 112.bundle.63e5c04ec6d607f1.7.js:2
Yl @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
Wl @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
Zl @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
(anonymous) @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
qu @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
Mu @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
ti @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
wu @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
enqueueSetState @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
v.setState @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
_rerenderFromProps @ 551.bundle.0caca870d182528d.58.js:2
(anonymous) @ 1488.bundle.0547de72319fc47e.58.js:2
y @ 926.bundle.c12abacf2f3f5495.7.js:2
v @ 926.bundle.c12abacf2f3f5495.7.js:2
p @ 926.bundle.c12abacf2f3f5495.7.js:2
u.trigger @ 926.bundle.c12abacf2f3f5495.7.js:2
trigger @ 112.bundle.63e5c04ec6d607f1.7.js:2
redraw @ 112.bundle.63e5c04ec6d607f1.7.js:2
R @ 8379.bundle.269039cf70f0f648.58.js:2
_handleDataFinished @ 8379.bundle.269039cf70f0f648.58.js:2
_handleAction @ platform-libs.bundle.be09cc208c8d82cc.58.js:2
i.$Dispatcher_invokeCallback @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
i.dispatch @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
dispatch @ platform-libs.bundle.be09cc208c8d82cc.58.js:2
ee @ 1816.bundle.841f1471cf59a19b.58.js:2
I @ 8379.bundle.269039cf70f0f648.58.js:2
d.context @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
x.<computed> @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
(anonymous) @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
m @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
h @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
l @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
u @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
e.put @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
N.e.put @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
(anonymous) @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
dispatch @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
(anonymous) @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
(anonymous) @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
p @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
E @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
h @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
x.<computed> @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
(anonymous) @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
m @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
h @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
l @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
u @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
put @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
(anonymous) @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
(anonymous) @ 4662.bundle.ed4e97988b8da3da.58.js:2
m @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
h @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
u @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
s.trigger @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
forwardDone @ 6145.bundle.d39e5e2e15c87297.58.js:2
_handleMessageResponse @ 6145.bundle.d39e5e2e15c87297.58.js:2
(anonymous) @ queryworkerbootstrap.1e4df2e59f467925caa6.6.js:2
Promise.then
(anonymous) @ queryworkerbootstrap.1e4df2e59f467925caa6.6.js:2
invoke @ queryworkerbootstrap.1e4df2e59f467925caa6.6.js:2
_invokeCallbacks @ queryworkerbootstrap.1e4df2e59f467925caa6.6.js:2
WorkerListenerClient._worker.onmessage @ queryworkerbootstrap.1e4df2e59f467925caa6.6.js:2
580.bundle.0eaeb8ea1ce2d735.7.js:2 [ECharts] DEPRECATED: textStyle hierarchy in axisLabel has been removed since 4.0. All textStyle properties are configured in axisLabel directly now.
Io @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
Do @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
Yd @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
(anonymous) @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
B @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
(anonymous) @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
B @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
qd @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
of @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
(anonymous) @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
B @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
p @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
(anonymous) @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
t.setOption @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
e.setOption @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
e.setOption @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
V @ 528.bundle.e98fed477fe80595.7.js:2
R @ 528.bundle.e98fed477fe80595.7.js:2
(anonymous) @ 112.bundle.63e5c04ec6d607f1.7.js:2
Promise.then
_rerender @ 112.bundle.63e5c04ec6d607f1.7.js:2
m @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
g @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
_ @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
componentDidMount @ 112.bundle.63e5c04ec6d607f1.7.js:2
Yl @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
Wl @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
Zl @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
(anonymous) @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
qu @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
Mu @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
ti @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
wu @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
enqueueSetState @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
v.setState @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
_rerenderFromProps @ 551.bundle.0caca870d182528d.58.js:2
(anonymous) @ 1488.bundle.0547de72319fc47e.58.js:2
y @ 926.bundle.c12abacf2f3f5495.7.js:2
v @ 926.bundle.c12abacf2f3f5495.7.js:2
p @ 926.bundle.c12abacf2f3f5495.7.js:2
u.trigger @ 926.bundle.c12abacf2f3f5495.7.js:2
trigger @ 112.bundle.63e5c04ec6d607f1.7.js:2
redraw @ 112.bundle.63e5c04ec6d607f1.7.js:2
R @ 8379.bundle.269039cf70f0f648.58.js:2
_handleDataFinished @ 8379.bundle.269039cf70f0f648.58.js:2
_handleAction @ platform-libs.bundle.be09cc208c8d82cc.58.js:2
i.$Dispatcher_invokeCallback @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
i.dispatch @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
dispatch @ platform-libs.bundle.be09cc208c8d82cc.58.js:2
ee @ 1816.bundle.841f1471cf59a19b.58.js:2
I @ 8379.bundle.269039cf70f0f648.58.js:2
d.context @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
x.<computed> @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
(anonymous) @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
m @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
h @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
l @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
u @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
e.put @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
N.e.put @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
(anonymous) @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
dispatch @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
(anonymous) @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
(anonymous) @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
p @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
E @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
h @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
x.<computed> @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
(anonymous) @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
m @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
h @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
l @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
u @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
put @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
(anonymous) @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
(anonymous) @ 4662.bundle.ed4e97988b8da3da.58.js:2
m @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
h @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
u @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
s.trigger @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
forwardDone @ 6145.bundle.d39e5e2e15c87297.58.js:2
_handleMessageResponse @ 6145.bundle.d39e5e2e15c87297.58.js:2
(anonymous) @ queryworkerbootstrap.1e4df2e59f467925caa6.6.js:2
Promise.then
(anonymous) @ queryworkerbootstrap.1e4df2e59f467925caa6.6.js:2
invoke @ queryworkerbootstrap.1e4df2e59f467925caa6.6.js:2
_invokeCallbacks @ queryworkerbootstrap.1e4df2e59f467925caa6.6.js:2
WorkerListenerClient._worker.onmessage @ queryworkerbootstrap.1e4df2e59f467925caa6.6.js:2
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796
quicksight-visual-sdk.tsx:157 Failed to apply runtime theme: SET_THEME_OVERRIDE timed out
overrideMethod @ hook.js:608
applyRuntimeTheme @ quicksight-visual-sdk.tsx:157
await in applyRuntimeTheme
(anonymous) @ quicksight-visual-sdk.tsx:206
commitHookEffectListMount @ chunk-WERSD76P.js?v=8d0075ee:16915
commitPassiveMountOnFiber @ chunk-WERSD76P.js?v=8d0075ee:18156
commitPassiveMountEffects_complete @ chunk-WERSD76P.js?v=8d0075ee:18129
commitPassiveMountEffects_begin @ chunk-WERSD76P.js?v=8d0075ee:18119
commitPassiveMountEffects @ chunk-WERSD76P.js?v=8d0075ee:18109
flushPassiveEffectsImpl @ chunk-WERSD76P.js?v=8d0075ee:19490
flushPassiveEffects @ chunk-WERSD76P.js?v=8d0075ee:19447
(anonymous) @ chunk-WERSD76P.js?v=8d0075ee:19328
workLoop @ chunk-WERSD76P.js?v=8d0075ee:197
flushWork @ chunk-WERSD76P.js?v=8d0075ee:176
performWorkUntilDeadline @ chunk-WERSD76P.js?v=8d0075ee:384
