quicksight-visual-sdk.tsx:156 Failed to apply runtime theme: SET_THEME_OVERRIDE timed out
override<PERSON>eth<PERSON> @ hook.js:608
applyRuntimeTheme @ quicksight-visual-sdk.tsx:156Understand this error
63hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796Understand this warning
real-data-filter-controls.tsx:98 Generated filters based on your actual data: [{…}]
home.tsx:53 Filters updated: [{…}]
quicksight-visual-sdk.tsx:212 Filters changed, re-initializing QuickSight visual...
quicksight-visual-sdk.tsx:55 Initializing QuickSight with Runtime Theming...
quicksight-runtime-theming.ts:128 Generated theme configuration for yellow: {DataColorPalette: {…}}
quicksight-visual-sdk.tsx:65 Applying theme options: {themeOverride: {…}, preloadThemes: Array(3)}
quicksight-runtime-filtering.ts:184 Parsed filters from URL: []
quicksight-visual-sdk.tsx:71 Applying runtime filters: [{…}]
quicksight-visual-sdk.tsx:103 Embedding with theme options: {themeOverride: {…}, preloadThemes: Array(3)}
quicksight-visual-sdk.tsx:111 QuickSight embedded with runtime theming and filtering successfully
quicksight-visual-sdk.tsx:119 Runtime filters applied successfully
quicksight-visual-sdk.tsx:123 Runtime theme and filter configuration applied successfully
quicksight-runtime-theming.ts:128 Generated theme configuration for yellow: {DataColorPalette: {…}}
quicksight-visual-sdk.tsx:143 Applying runtime theme override for yellow scheme
580.bundle.0eaeb8ea1ce2d735.7.js:2 [ECharts] Can't get DOM width or height. Please check dom.clientWidth and dom.clientHeight. They should not be 0.For example, you may need to call this in the callback of window.onload.
Io @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
To @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
Am @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
R @ 528.bundle.e98fed477fe80595.7.js:2
(anonymous) @ 112.bundle.63e5c04ec6d607f1.7.js:2
Promise.then
_rerender @ 112.bundle.63e5c04ec6d607f1.7.js:2
m @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
g @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
_ @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
componentDidUpdate @ 112.bundle.63e5c04ec6d607f1.7.js:2
Yl @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
Wl @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
Zl @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
(anonymous) @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
qu @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
Mu @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
ti @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
wu @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
enqueueSetState @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
v.setState @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
_setResizeState @ 8769.bundle.1d0963b30e5f3879.58.js:2
s @ 4340.bundle.ce5b70743c5e7bb5.58.js:2
r @ 4340.bundle.ce5b70743c5e7bb5.58.js:2
m @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
g @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
_ @ platform-vendors.bundle.0092b16f0b934b51.58.js:2Understand this warning
20hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796Understand this warning
580.bundle.0eaeb8ea1ce2d735.7.js:2 [ECharts] DEPRECATED: 'normal' hierarchy in itemStyle has been removed since 4.0. All style properties are configured in itemStyle directly now.
Io @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
Do @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
Wd @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
Ud @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
(anonymous) @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
(anonymous) @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
B @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
qd @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
of @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
(anonymous) @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
B @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
p @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
(anonymous) @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
t.setOption @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
e.setOption @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
e.setOption @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
V @ 528.bundle.e98fed477fe80595.7.js:2
R @ 528.bundle.e98fed477fe80595.7.js:2
(anonymous) @ 112.bundle.63e5c04ec6d607f1.7.js:2
Promise.then
_rerender @ 112.bundle.63e5c04ec6d607f1.7.js:2
m @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
g @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
_ @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
componentDidMount @ 112.bundle.63e5c04ec6d607f1.7.js:2
Yl @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
Wl @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
Zl @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
(anonymous) @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
qu @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
Mu @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
ti @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
wu @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
enqueueSetState @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
v.setState @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
_rerenderFromProps @ 551.bundle.0caca870d182528d.58.js:2
(anonymous) @ 1488.bundle.0547de72319fc47e.58.js:2
y @ 926.bundle.c12abacf2f3f5495.7.js:2
v @ 926.bundle.c12abacf2f3f5495.7.js:2
p @ 926.bundle.c12abacf2f3f5495.7.js:2
u.trigger @ 926.bundle.c12abacf2f3f5495.7.js:2
trigger @ 112.bundle.63e5c04ec6d607f1.7.js:2
redraw @ 112.bundle.63e5c04ec6d607f1.7.js:2
R @ 8379.bundle.269039cf70f0f648.58.js:2
_handleDataFinished @ 8379.bundle.269039cf70f0f648.58.js:2
_handleAction @ platform-libs.bundle.be09cc208c8d82cc.58.js:2
i.$Dispatcher_invokeCallback @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
i.dispatch @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
dispatch @ platform-libs.bundle.be09cc208c8d82cc.58.js:2
ee @ 1816.bundle.841f1471cf59a19b.58.js:2
I @ 8379.bundle.269039cf70f0f648.58.js:2
d.context @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
x.<computed> @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
(anonymous) @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
m @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
h @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
l @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
u @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
e.put @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
N.e.put @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
(anonymous) @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
dispatch @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
(anonymous) @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
(anonymous) @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
p @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
E @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
h @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
x.<computed> @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
(anonymous) @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
m @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
h @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
l @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
u @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
put @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
(anonymous) @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
(anonymous) @ 4662.bundle.ed4e97988b8da3da.58.js:2
m @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
h @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
u @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
s.trigger @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
forwardDone @ 6145.bundle.d39e5e2e15c87297.58.js:2
_handleMessageResponse @ 6145.bundle.d39e5e2e15c87297.58.js:2
(anonymous) @ queryworkerbootstrap.1e4df2e59f467925caa6.6.js:2
Promise.then
(anonymous) @ queryworkerbootstrap.1e4df2e59f467925caa6.6.js:2
invoke @ queryworkerbootstrap.1e4df2e59f467925caa6.6.js:2
_invokeCallbacks @ queryworkerbootstrap.1e4df2e59f467925caa6.6.js:2
WorkerListenerClient._worker.onmessage @ queryworkerbootstrap.1e4df2e59f467925caa6.6.js:2Understand this warning
580.bundle.0eaeb8ea1ce2d735.7.js:2 [ECharts] DEPRECATED: textStyle hierarchy in axisLabel has been removed since 4.0. All textStyle properties are configured in axisLabel directly now.
Io @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
Do @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
Yd @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
(anonymous) @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
B @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
(anonymous) @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
B @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
qd @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
of @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
(anonymous) @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
B @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
p @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
(anonymous) @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
t.setOption @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
e.setOption @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
e.setOption @ 580.bundle.0eaeb8ea1ce2d735.7.js:2
V @ 528.bundle.e98fed477fe80595.7.js:2
R @ 528.bundle.e98fed477fe80595.7.js:2
(anonymous) @ 112.bundle.63e5c04ec6d607f1.7.js:2
Promise.then
_rerender @ 112.bundle.63e5c04ec6d607f1.7.js:2
m @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
g @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
_ @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
componentDidMount @ 112.bundle.63e5c04ec6d607f1.7.js:2
Yl @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
Wl @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
Zl @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
(anonymous) @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
qu @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
Mu @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
ti @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
wu @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
enqueueSetState @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
v.setState @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
_rerenderFromProps @ 551.bundle.0caca870d182528d.58.js:2
(anonymous) @ 1488.bundle.0547de72319fc47e.58.js:2
y @ 926.bundle.c12abacf2f3f5495.7.js:2
v @ 926.bundle.c12abacf2f3f5495.7.js:2
p @ 926.bundle.c12abacf2f3f5495.7.js:2
u.trigger @ 926.bundle.c12abacf2f3f5495.7.js:2
trigger @ 112.bundle.63e5c04ec6d607f1.7.js:2
redraw @ 112.bundle.63e5c04ec6d607f1.7.js:2
R @ 8379.bundle.269039cf70f0f648.58.js:2
_handleDataFinished @ 8379.bundle.269039cf70f0f648.58.js:2
_handleAction @ platform-libs.bundle.be09cc208c8d82cc.58.js:2
i.$Dispatcher_invokeCallback @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
i.dispatch @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
dispatch @ platform-libs.bundle.be09cc208c8d82cc.58.js:2
ee @ 1816.bundle.841f1471cf59a19b.58.js:2
I @ 8379.bundle.269039cf70f0f648.58.js:2
d.context @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
x.<computed> @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
(anonymous) @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
m @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
h @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
l @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
u @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
e.put @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
N.e.put @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
(anonymous) @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
dispatch @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
(anonymous) @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
(anonymous) @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
p @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
E @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
h @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
x.<computed> @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
(anonymous) @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
m @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
h @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
l @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
u @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
put @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
(anonymous) @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
(anonymous) @ 4662.bundle.ed4e97988b8da3da.58.js:2
m @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
h @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
u @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
s.trigger @ platform-vendors.bundle.0092b16f0b934b51.58.js:2
forwardDone @ 6145.bundle.d39e5e2e15c87297.58.js:2
_handleMessageResponse @ 6145.bundle.d39e5e2e15c87297.58.js:2
(anonymous) @ queryworkerbootstrap.1e4df2e59f467925caa6.6.js:2
Promise.then
(anonymous) @ queryworkerbootstrap.1e4df2e59f467925caa6.6.js:2
invoke @ queryworkerbootstrap.1e4df2e59f467925caa6.6.js:2
_invokeCallbacks @ queryworkerbootstrap.1e4df2e59f467925caa6.6.js:2
WorkerListenerClient._worker.onmessage @ queryworkerbootstrap.1e4df2e59f467925caa6.6.js:2Understand this warning
16hook.js:608 Message with unrecognized event target received
overrideMethod @ hook.js:608
(anonymous) @ amazon-quicksight-embedding-sdk.js?v=8d0075ee:1796Understand this warning
quicksight-visual-sdk.tsx:156 Failed to apply runtime theme: SET_THEME_OVERRIDE timed out