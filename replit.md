# QuickSight Embedding SDK Demo Application

## Overview
A professional web application that demonstrates Amazon QuickSight Embedding SDK by showcasing individual visual embedding with runtime theme customization and AWS-inspired design.

## Project Architecture
- **Frontend**: React + TypeScript with Vite
- **Backend**: Express.js with AWS SDK integration
- **Styling**: Tailwind CSS + shadcn/ui components
- **QuickSight**: Amazon QuickSight Embedding SDK v2.10+

## QuickSight Configuration
- **Dashboard ID**: `b991ee42-96e1-4591-b999-f5457b751d07`
- **Sheet ID**: `b991ee42-96e1-4591-b999-f5457b751d07_26c2afbe-9a56-4226-87bd-e776ece85ae7`
- **Visual ID**: `b991ee42-96e1-4591-b999-f5457b751d07_6b78e4ed-c15d-4023-854d-386e496163d4`
- **Account ID**: `************`
- **Directory Alias**: `ARTBA2025`

## Features
- Individual QuickSight visual embedding using official SDK
- Runtime theme customization with precise color control
- URL-based filtering with automatic parameter parsing
- AWS-inspired design system with authentic theming
- Responsive layout optimized for embedded visuals
- Professional dashboard interface with theme override and filtering capabilities
- Shareable URLs with embedded filter state

## Color Scheme (AWS-Inspired)
- Primary: #FF9900 (AWS Orange)
- Secondary: #232F3E (AWS Dark Blue)
- Background: #FAFAFA (Light Grey)
- Text: #16191F (Dark Grey)
- Accent: #146EB4 (AWS Blue)
- Border: #D5DBDB (Light Border)

## Recent Changes
- 2025-01-17: Project initialized with QuickSight embedding requirements
- 2025-01-17: Dashboard and Visual IDs configured for embedding
- 2025-01-17: Implemented iframe-based embedding approach with proper error handling
- 2025-01-17: Added domain allow-listing setup instructions and AWS configuration guide
- 2025-01-17: Implemented intelligent embedding fallback system (authenticated → registered user → individual visual public URLs)
- 2025-01-17: Successfully targeting individual visual instead of full dashboard using enhanced URL construction
- 2025-01-17: Added comprehensive error handling for pricing plan limitations and IAM permission issues
- 2025-01-17: Successfully created custom yellow QuickSight theme via API
- 2025-01-17: Set up IAM role QuickSightEmbeddingRole for authenticated embedding and branding removal


## User Preferences
- Individual visual embedding with official QuickSight SDK (not iframe)
- Professional AWS-style design with authentic theming
- Runtime theme switching using official QuickSight Runtime Theming
- Precise color control without dashboard modifications
- Clean, streamlined implementation using only AWS-standard approaches
- DataColorPalette-only theming: Change only chart/bar colors, not UI backgrounds
- Focus on data visualization elements (bars, lines, points) rather than interface elements