# IAM Role Setup for QuickSight Authenticated Embedding

## What the IAM Role Does:
- **Removes "Powered by QuickSight" branding**
- **Enables full color customization via API**
- **Allows individual visual control** 
- **No Capacity Pricing required ($250/month savings)**

## Step-by-Step Setup:

### 1. Create IAM Role in AWS Console

**Go to:** AWS Console → IAM → Roles → Create Role

**Step 1 - Trust Entity:**
```json
{
    "Version": "2012-10-17",
    "Statement": [
        {
            "Effect": "Allow",
            "Principal": {
                "AWS": "arn:aws:iam::************:root"
            },
            "Action": "sts:AssumeRole"
        }
    ]
}
```

**Step 2 - Permissions Policy:**
Create and attach this policy:
```json
{
    "Version": "2012-10-17",
    "Statement": [
        {
            "Effect": "Allow",
            "Action": [
                "quicksight:RegisterUser",
                "quicksight:GetDashboardEmbedUrl",
                "quicksight:GetAuthCode",
                "quicksight:GetSessionEmbedUrl"
            ],
            "Resource": "*"
        }
    ]
}
```

**Step 3 - Role Name:**
`QuickSightEmbeddingRole`

### 2. Add Environment Variable

Add this to your environment:
```
AWS_ROLE_ARN=arn:aws:iam::************:role/QuickSightEmbeddingRole
```

### 3. Test Registered User Embedding

Once the role exists, the system will automatically:
- ✅ Use IAM role for authenticated embedding
- ✅ Remove QuickSight branding
- ✅ Enable full color customization
- ✅ Target individual visuals only

## Current Status:
- **Enterprise Edition**: ✅ Available
- **Individual Visual Targeting**: ✅ Working
- **Yellow Theme URL Parameters**: ✅ Working  
- **IAM Role**: ❌ Needs to be created in your account
- **Branding Removal**: ❌ Requires IAM role or Capacity Pricing

## Alternative: Dashboard Theme Modification

If you prefer not to create IAM role:
1. Go to QuickSight Console
2. Open your dashboard: `b991ee42-96e1-4591-b999-f5457b751d07`
3. Edit → Themes → Create custom theme
4. Set colors to yellow palette
5. Apply theme to dashboard

This will change colors but keep "Powered by QuickSight" branding.