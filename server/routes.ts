import type { Express } from "express";
import { createServer, type Server } from "http";
import { storage } from "./storage";
import AWS from "aws-sdk";

// Configure AWS QuickSight client
const quicksight = new AWS.QuickSight({
  region: process.env.AWS_REGION,
  accessKeyId: process.env.AWS_ACCESS_KEY_ID,
  secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY
});

// Also configure a QuickSight client for dataset operations
const quicksightDataset = new AWS.QuickSight({
  region: process.env.AWS_REGION,
  accessKeyId: process.env.AWS_ACCESS_KEY_ID,
  secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY
});

export async function registerRoutes(app: Express): Promise<Server> {
  // QuickSight authenticated embed URL generation endpoint
  app.post('/api/quicksight/embed-url', async (req, res) => {
    try {
      const { dashboardId, visualId, sheetId, accountId, directoryAlias, colorScheme = 'yellow', urlParameters, embedType = 'dashboard' } = req.body;
      
      console.log(`Generating QuickSight embed URL for ${embedType}:`, embedType === 'visual' ? visualId : dashboardId);
      if (urlParameters) {
        console.log('URL Parameters requested:', urlParameters);
      }
      
      // Check account pricing plan and try authenticated embedding first
      try {
        console.log('Attempting authenticated embedding...');
        
        // Configure experience based on embed type
        let experienceConfig: any = {};
        if (embedType === 'visual' && visualId && sheetId) {
          experienceConfig = {
            DashboardVisual: {
              InitialDashboardVisualId: {
                DashboardId: dashboardId,
                SheetId: sheetId,
                VisualId: visualId
              }
            }
          };
          console.log('Using DashboardVisual experience for visual embedding');
          console.log('Visual configuration:', JSON.stringify(experienceConfig, null, 2));
        } else {
          experienceConfig = {
            Dashboard: {
              InitialDashboardId: dashboardId
            }
          };
          console.log('Using Dashboard experience for full dashboard embedding');
        }
        
        const params = {
          AwsAccountId: accountId,
          Namespace: 'default',
          SessionLifetimeInMinutes: 600,
          ExperienceConfiguration: experienceConfig,
          AuthorizedResourceArns: [
            `arn:aws:quicksight:${process.env.AWS_REGION}:${accountId}:dashboard/${dashboardId}`
          ]
        };

        const result = await quicksight.generateEmbedUrlForAnonymousUser(params as any).promise();
        
        console.log('Successfully generated authenticated embed URL');
        
        // Append URL parameters if provided
        let finalEmbedUrl = result.EmbedUrl;
        if (urlParameters && Object.keys(urlParameters).length > 0) {
          const paramString = Object.entries(urlParameters)
            .map(([key, value]) => {
              if (Array.isArray(value)) {
                return value.map(v => `p.${key}=${encodeURIComponent(v)}`).join('&');
              }
              return `p.${key}=${encodeURIComponent(value as string)}`;
            })
            .join('&');
          
          finalEmbedUrl = `${result.EmbedUrl}#${paramString}`;
          console.log('Added URL parameters to embed URL:', paramString);
        }
        
        res.json({
          success: true,
          embedUrl: finalEmbedUrl,
          requestId: result.RequestId,
          colorScheme: colorScheme,
          type: 'authenticated',
          method: 'aws-api',
          embedType: embedType
        });
        return;
        
      } catch (authError: any) {
        console.log('Authenticated embedding failed:', authError.code);
        
        if (authError.code === 'UnsupportedPricingPlanException') {
          console.log('Account requires Capacity Pricing for authenticated embedding.');
          
          // Try using generateEmbedUrlForRegisteredUser instead for per-session pricing
          try {
            console.log('Attempting registered user embedding...');
            
            // Use the registered QuickSight user ARN instead of IAM role ARN
            const quicksightUserArn = `arn:aws:quicksight:${process.env.AWS_REGION}:${accountId}:user/default/QuickSightEmbeddingRole/QuickSightEmbeddingSession`;
            
            // Configure experience based on embed type
            let experienceConfig: any = {};
            if (embedType === 'visual' && visualId && sheetId) {
              experienceConfig = {
                DashboardVisual: {
                  InitialDashboardVisualId: {
                    DashboardId: dashboardId,
                    SheetId: sheetId,
                    VisualId: visualId
                  }
                }
              };
              console.log('Registered User: Using DashboardVisual experience for visual embedding');
              console.log('Visual configuration:', JSON.stringify(experienceConfig, null, 2));
            } else {
              experienceConfig = {
                Dashboard: {
                  InitialDashboardId: dashboardId
                }
              };
              console.log('Registered User: Using Dashboard experience for full dashboard embedding');
            }
            
            const registeredUserParams = {
              AwsAccountId: accountId,
              UserArn: quicksightUserArn,
              SessionLifetimeInMinutes: 600,
              ExperienceConfiguration: experienceConfig
            };

            // Note: Theme application requires dashboard-level configuration
            // Colors will be changed through URL parameters and programmatic theme updates

            const registeredResult = await quicksight.generateEmbedUrlForRegisteredUser(registeredUserParams as any).promise();
            
            console.log('Successfully generated registered user embed URL');
            
            // Append URL parameters if provided
            let finalEmbedUrl = registeredResult.EmbedUrl;
            if (urlParameters && Object.keys(urlParameters).length > 0) {
              const paramString = Object.entries(urlParameters)
                .map(([key, value]) => {
                  if (Array.isArray(value)) {
                    return value.map(v => `p.${key}=${encodeURIComponent(v)}`).join('&');
                  }
                  return `p.${key}=${encodeURIComponent(value as string)}`;
                })
                .join('&');
              
              finalEmbedUrl = `${registeredResult.EmbedUrl}#${paramString}`;
              console.log('Added URL parameters to embed URL:', paramString);
            }
            
            // Apply yellow theme if requested
            if (colorScheme === 'yellow') {
              try {
                console.log('Applying yellow theme to dashboard...');
                const updateDashboardParams = {
                  AwsAccountId: accountId,
                  DashboardId: dashboardId,
                  Name: 'Dashboard with Theme',
                  ThemeArn: `arn:aws:quicksight:us-east-1:${accountId}:theme/custom-yellow-theme`
                };
                
                // Attempt to apply theme (fire and forget)
                quicksight.updateDashboard(updateDashboardParams).promise()
                  .then(() => console.log('Yellow theme applied successfully'))
                  .catch(err => console.log('Theme application note:', err.code));
              } catch (themeError) {
                console.log('Theme application attempted but not critical');
              }
            }
            
            res.json({
              success: true,
              embedUrl: finalEmbedUrl,
              requestId: registeredResult.RequestId,
              colorScheme: colorScheme,
              type: 'registered_user',
              method: 'aws-api-registered',
              embedType: embedType,
              visualId: embedType === 'visual' ? visualId : undefined,
              sheetId: embedType === 'visual' ? sheetId : undefined,
              dashboardId: dashboardId,
              features: [
                embedType === 'visual' ? 'Individual visual embedding' : 'Full dashboard embedding',
                'Authenticated embedding - NO "Powered by QuickSight" branding',
                'Full color customization via QuickSight themes',
                'No Capacity Pricing required'
              ],
              note: `SUCCESS: Using registered user embedding for ${embedType}`
            });
            return;
            
          } catch (registeredError: any) {
            console.log('Registered user embedding also failed:', registeredError.code);
            console.log('Full error:', registeredError.message);
            
            // Try public embedding as a fallback
            console.log('Attempting public embedding fallback...');
            
            // Generate a public embed URL based on embed type
            let publicEmbedUrl: string;
            if (embedType === 'visual' && visualId && sheetId) {
              // For visual embedding, use the visual-specific URL format
              publicEmbedUrl = `https://us-east-1.quicksight.aws.amazon.com/sn/embed/share/accounts/${accountId}/dashboards/${dashboardId}/sheets/${sheetId}/visuals/${visualId}?directory_alias=${directoryAlias}`;
            } else {
              // For dashboard embedding
              publicEmbedUrl = `https://us-east-1.quicksight.aws.amazon.com/sn/embed/share/accounts/${accountId}/dashboards/${dashboardId}?directory_alias=${directoryAlias}`;
            }
            
            // Append URL parameters if provided
            let finalEmbedUrl = publicEmbedUrl;
            if (urlParameters && Object.keys(urlParameters).length > 0) {
              const paramString = Object.entries(urlParameters)
                .map(([key, value]) => {
                  if (Array.isArray(value)) {
                    return value.map(v => `p.${key}=${encodeURIComponent(v)}`).join('&');
                  }
                  return `p.${key}=${encodeURIComponent(value as string)}`;
                })
                .join('&');
              
              finalEmbedUrl = `${publicEmbedUrl}#${paramString}`;
              console.log('Added URL parameters to public embed URL:', paramString);
            }
            
            res.json({
              success: true,
              embedUrl: finalEmbedUrl,
              colorScheme: colorScheme,
              type: 'public_embed',
              method: 'public-share-url',
              embedType: embedType,
              features: [
                embedType === 'visual' ? 'Public visual embedding' : 'Public dashboard embedding',
                'URL parameter filtering support',
                'Compatible with QuickSight Embedding SDK',
                'No authentication required'
              ],
              note: `Using public embedding for ${embedType} - upgrade to authenticated embedding for full customization`
            });
            return;
          }
        }
        
        throw authError;
      }
      
    } catch (error: any) {
      console.error('Error generating embed URL:', error);
      res.status(500).json({
        error: error.message || 'Failed to generate embed URL',
        success: false,
        type: 'error',
        code: error.code
      });
    }
  });

  // Health check endpoint
  app.get('/api/health', (req, res) => {
    res.json({
      status: 'ok',
      timestamp: new Date().toISOString(),
      version: '2.10.1'
    });
  });

  // Dataset inspection endpoint
  app.get('/api/quicksight/inspect-dashboard/:accountId/:dashboardId', async (req, res) => {
    try {
      const { accountId, dashboardId } = req.params;
      
      console.log(`Inspecting dashboard ${dashboardId} for available filters...`);
      
      const { inspectDashboardDefinition, listDataSets } = await import('./dataset-inspector');
      
      // Get dashboard definition
      const dashboardInfo = await inspectDashboardDefinition(accountId, dashboardId);
      
      // Get available datasets
      const datasets = await listDataSets(accountId);
      
      res.json({
        success: true,
        dashboardInfo,
        availableDatasets: datasets,
        message: 'Dashboard inspection completed'
      });
      
    } catch (error: any) {
      console.error('Error inspecting dashboard:', error);
      res.status(500).json({
        error: error.message || 'Failed to inspect dashboard',
        success: false
      });
    }
  });

  // Dataset columns inspection endpoint  
  app.get('/api/quicksight/inspect-dataset/:accountId/:dataSetId', async (req, res) => {
    try {
      const { accountId, dataSetId } = req.params;
      
      console.log(`Inspecting dataset ${dataSetId} for available columns...`);
      
      const { inspectDataSet } = await import('./dataset-inspector');
      
      const datasetInfo = await inspectDataSet(accountId, dataSetId);
      
      res.json({
        success: true,
        datasetInfo,
        availableColumns: datasetInfo.columns,
        message: 'Dataset inspection completed'
      });
      
    } catch (error: any) {
      console.error('Error inspecting dataset:', error);
      res.status(500).json({
        error: error.message || 'Failed to inspect dataset',
        success: false
      });
    }
  });

  // Create or update QuickSight theme for color customization  
  app.post('/api/quicksight/create-theme', async (req: any, res: any) => {
    try {
      const { accountId, themeId, colorScheme } = req.body;
      
      if (!accountId || !themeId || !colorScheme) {
        return (res as any).status(400).json({ 
          error: 'Missing required parameters: accountId, themeId, colorScheme' 
        });
      }

      console.log(`Creating QuickSight theme: ${themeId} with ${colorScheme} colors`);

      // Yellow theme configuration for QuickSight API
      const yellowThemeConfig: any = {
        DataColorPalette: {
          Colors: [
            "#FFD700", "#FFA500", "#FFFF00", "#F0E68C", 
            "#DAA520", "#B8860B", "#FFE4B5", "#FFEFD5"
          ],
          MinMaxGradient: ["#FFD700", "#B8860B"],
          EmptyFillColor: "#FFFACD"
        },
        UIColorPalette: {
          PrimaryForeground: "#8B4513",
          PrimaryBackground: "#FFD700", 
          SecondaryForeground: "#654321", 
          SecondaryBackground: "#FFA500",
          Accent: "#DAA520",
          AccentForeground: "#FFFFFF",
          Danger: "#FF6B6B",
          DangerForeground: "#FFFFFF",
          Warning: "#FFE066",
          WarningForeground: "#8B4513",
          Success: "#51CF66",
          SuccessForeground: "#FFFFFF",
          Dimension: "#8B4513",
          DimensionForeground: "#FFFACD",
          Measure: "#DAA520",
          MeasureForeground: "#FFFFFF"
        }
      };

      const createThemeParams: any = {
        AwsAccountId: accountId,
        ThemeId: themeId,
        Name: `${colorScheme}-custom-theme`,
        BaseThemeId: 'MIDNIGHT', // Required base theme
        Configuration: yellowThemeConfig,
        Tags: [
          { Key: 'CreatedBy', Value: 'QuickSightEmbeddingApp' },
          { Key: 'ColorScheme', Value: colorScheme }
        ]
      };

      try {
        const result = await quicksight.createTheme(createThemeParams).promise();
        console.log('Successfully created theme:', result.ThemeId);
        
        (res as any).json({
          success: true,
          themeId: result.ThemeId,
          arn: result.Arn,
          action: 'created',
          colorScheme: colorScheme,
          message: 'Theme created successfully - apply it to your dashboard in QuickSight console'
        });
        
      } catch (createError: any) {
        if (createError.code === 'ResourceExistsException') {
          const updateParams: any = {
            AwsAccountId: accountId,
            ThemeId: themeId,
            Name: `${colorScheme}-theme-updated`,
            Configuration: yellowThemeConfig
          };
          
          const updateResult = await quicksight.updateTheme(updateParams).promise();
          console.log('Successfully updated existing theme:', updateResult.ThemeId);
          
          (res as any).json({
            success: true,
            themeId: updateResult.ThemeId,
            arn: updateResult.Arn,
            action: 'updated',
            colorScheme: colorScheme,
            message: 'Theme updated successfully - changes will apply to dashboard'
          });
        } else {
          throw createError;
        }
      }
      
    } catch (error: any) {
      console.error('Theme creation/update failed:', error);
      (res as any).status(500).json({ 
        error: 'Failed to create/update theme',
        code: error.code,
        message: error.message
      });
    }
  });

  // Get dataset columns and information for debugging filters
  app.post('/api/quicksight/dataset-columns', async (req, res) => {
    try {
      const { dashboardId } = req.body;
      const accountId = "************"; // Your account ID
      
      console.log('Fetching dataset information for dashboard:', dashboardId);
      
      // First, try to get dashboard information
      try {
        const dashboardParams = {
          AwsAccountId: accountId,
          DashboardId: dashboardId
        };
        
        const dashboardInfo = await quicksightDataset.describeDashboardDefinition(dashboardParams).promise();
        console.log('Dashboard definition retrieved');
        console.log('Definition structure:', JSON.stringify({
          hasDefinition: !!dashboardInfo.Definition,
          hasDataSetDeclarations: !!dashboardInfo.Definition?.DataSetIdentifierDeclarations,
          dataSetDeclarationsCount: dashboardInfo.Definition?.DataSetIdentifierDeclarations?.length || 0,
          dataSetDeclarations: dashboardInfo.Definition?.DataSetIdentifierDeclarations?.map(ds => ({
            identifier: ds.Identifier,
            dataSetArn: ds.DataSetArn
          }))
        }, null, 2));
        
        // Get datasets used by this dashboard
        const datasets = [];
        if (dashboardInfo.Definition?.DataSetIdentifierDeclarations) {
          for (const datasetDeclaration of dashboardInfo.Definition.DataSetIdentifierDeclarations) {
            try {
              const datasetParams = {
                AwsAccountId: accountId,
                DataSetId: datasetDeclaration.DataSetArn?.split('/').pop() || datasetDeclaration.Identifier
              };
              
              const datasetInfo = await quicksightDataset.describeDataSet(datasetParams).promise();
              
              // Get column information
              const columns = datasetInfo.DataSet?.OutputColumns || [];
              
              datasets.push({
                id: datasetInfo.DataSet?.DataSetId,
                name: datasetInfo.DataSet?.Name,
                columns: columns.map(col => ({
                  name: col.Name,
                  type: col.Type,
                  description: col.Description
                }))
              });
              
            } catch (datasetError: any) {
              console.log(`Could not fetch dataset ${datasetDeclaration.Identifier}:`, datasetError.code);
            }
          }
        }
        
        res.json({
          success: true,
          dashboardId: dashboardId,
          dashboardName: dashboardInfo.Name || 'Unknown Dashboard',
          datasets: datasets,
          totalDatasets: datasets.length,
          message: 'Dataset information retrieved successfully'
        });
        
      } catch (dashboardError: any) {
        console.log('Could not fetch dashboard info:', dashboardError.code);
        
        // Fallback: try to list all datasets in the account
        try {
          const listParams = {
            AwsAccountId: accountId,
            MaxResults: 50
          };
          
          const datasetList = await quicksightDataset.listDataSets(listParams).promise();
          
          res.json({
            success: true,
            dashboardId: dashboardId,
            fallbackMode: true,
            availableDatasets: datasetList.DataSetSummaries?.map(ds => ({
              id: ds.DataSetId,
              name: ds.Name,
              createdTime: ds.CreatedTime,
              lastUpdatedTime: ds.LastUpdatedTime
            })),
            message: 'Could not get dashboard-specific datasets, showing all available datasets',
            note: 'Use the QuickSight console to identify which datasets are used by your dashboard'
          });
          
        } catch (listError: any) {
          throw new Error(`Could not fetch dashboard or dataset information: ${listError.code}`);
        }
      }
      
    } catch (error: any) {
      console.error('Dataset columns fetch failed:', error);
      res.status(500).json({ 
        error: 'Failed to fetch dataset columns',
        code: error.code,
        message: error.message,
        suggestion: 'Check your AWS credentials and permissions for QuickSight DescribeDashboard and DescribeDataSet operations'
      });
    }
  });

  const httpServer = createServer(app);
  return httpServer;
}
