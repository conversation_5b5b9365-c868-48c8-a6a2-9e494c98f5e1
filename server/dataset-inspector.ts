// QuickSight Dataset Inspector - Discover available columns and filters
import AWS from 'aws-sdk';

const quicksight = new AWS.QuickSight({ region: process.env.AWS_REGION || 'us-east-1' });

export async function inspectDashboardDefinition(accountId: string, dashboardId: string) {
  try {
    console.log('Inspecting dashboard definition for available columns...');
    
    const params = {
      AwsAccountId: accountId,
      DashboardId: dashboardId
    };

    const result = await quicksight.describeDashboardDefinition(params).promise();
    
    console.log('Dashboard Definition Result:', JSON.stringify(result, null, 2));
    
    // Extract dataset information
    const datasets = result.Definition?.DataSetIdentifierDeclarations || [];
    const sheets = result.Definition?.Sheets || [];
    
    console.log('Available Datasets:', datasets.map(ds => ({
      identifier: ds.Identifier,
      dataSetArn: ds.DataSetArn
    })));
    
    // Extract visual information
    const visuals = sheets.flatMap(sheet => 
      sheet.Visuals?.map(visual => ({
        visualId: (visual as any).VisualId,
        type: Object.keys(visual)[1], // Get the visual type (BarChartVisual, etc.)
        title: (visual as any).Title?.Visibility
      })) || []
    );
    
    console.log('Available Visuals:', visuals);
    
    return {
      datasets,
      sheets,
      visuals,
      definition: result.Definition
    };
    
  } catch (error) {
    console.error('Error inspecting dashboard definition:', error);
    throw error;
  }
}

export async function inspectDataSet(accountId: string, dataSetId: string) {
  try {
    console.log('Inspecting dataset for available columns...');
    
    const params = {
      AwsAccountId: accountId,
      DataSetId: dataSetId
    };

    const result = await quicksight.describeDataSet(params).promise();
    
    console.log('Dataset Result:', JSON.stringify(result, null, 2));
    
    // Extract column information
    const physicalTableMap = result.DataSet?.PhysicalTableMap || {};
    const logicalTableMap = result.DataSet?.LogicalTableMap || {};
    
    const availableColumns: string[] = [];
    
    // Get columns from physical tables
    Object.values(physicalTableMap).forEach((table: any) => {
      if (table.S3Source?.InputColumns) {
        table.S3Source.InputColumns.forEach((col: any) => {
          availableColumns.push(col.Name);
        });
      }
      if (table.RelationalTable?.InputColumns) {
        table.RelationalTable.InputColumns.forEach((col: any) => {
          availableColumns.push(col.Name);
        });
      }
    });
    
    console.log('Available Columns:', availableColumns);
    
    return {
      columns: availableColumns,
      physicalTableMap,
      logicalTableMap,
      dataSet: result.DataSet
    };
    
  } catch (error) {
    console.error('Error inspecting dataset:', error);
    throw error;
  }
}

export async function listDataSets(accountId: string) {
  try {
    console.log('Listing all datasets...');
    
    const params = {
      AwsAccountId: accountId
    };

    const result = await quicksight.listDataSets(params).promise();
    
    const datasets = result.DataSetSummaries?.map(ds => ({
      name: ds.Name,
      dataSetId: ds.DataSetId,
      arn: ds.Arn,
      createdTime: ds.CreatedTime,
      lastUpdatedTime: ds.LastUpdatedTime
    })) || [];
    
    console.log('Available DataSets:', datasets);
    
    return datasets;
    
  } catch (error) {
    console.error('Error listing datasets:', error);
    throw error;
  }
}